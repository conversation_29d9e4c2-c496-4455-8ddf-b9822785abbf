{"version": 3, "file": "mapping-entry.test.js", "sourceRoot": "", "sources": ["../../src/__tests__/mapping-entry.test.ts"], "names": [], "mappings": ";;AAAA,kDAA6D;AAC7D,6BAA4B;AAE5B,QAAQ,CAAC,eAAe,EAAE;IACxB,EAAE,CAAC,kEAAkE,EAAE;QACrE,IAAM,MAAM,GAAG,IAAA,yCAAyB,EACtC,oBAAoB,EACpB;YACE,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;YACvB,mBAAmB,EAAE,CAAC,WAAW,CAAC;YAClC,WAAW,EAAE,CAAC,OAAO,CAAC;SACvB,EACD,IAAI,CACL,CAAC;QACF,6BAA6B;QAC7B,MAAM;QACN,oCAAoC;QACpC,gEAAgE;QAChE,OAAO;QACP,MAAM;QACN,4BAA4B;QAC5B,yDAAyD;QACzD,OAAO;QACP,MAAM;QACN,oBAAoB;QACpB,eAAe;QACf,kDAAkD;QAClD,kDAAkD;QAClD,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;YACrB;gBACE,OAAO,EAAE,mBAAmB;gBAC5B,KAAK,EAAE,CAAC,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;aACzD;YACD;gBACE,OAAO,EAAE,WAAW;gBACpB,KAAK,EAAE,CAAC,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;aAClD;YACD;gBACE,OAAO,EAAE,GAAG;gBACZ,KAAK,EAAE;oBACL,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;oBACxC,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;iBACzC;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sDAAsD,EAAE;QACzD,IAAI,MAAM,GAAG,IAAA,yCAAyB,EAAC,oBAAoB,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;QACvE,6BAA6B;QAC7B,MAAM;QACN,oBAAoB;QACpB,sDAAsD;QACtD,OAAO;QACP,MAAM;QACN,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;YACrB;gBACE,OAAO,EAAE,GAAG;gBACZ,KAAK,EAAE,CAAC,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;aAC/C;SACF,CAAC,CAAC;QAEH,MAAM,GAAG,IAAA,yCAAyB,EAAC,oBAAoB,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QACpE,gCAAgC;QAChC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}