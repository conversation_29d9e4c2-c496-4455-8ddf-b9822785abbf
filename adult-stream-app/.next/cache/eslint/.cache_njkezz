[{"/Users/<USER>/Projects/release/adult-stream-app/src/app/categories/page.tsx": "1", "/Users/<USER>/Projects/release/adult-stream-app/src/app/layout.tsx": "2", "/Users/<USER>/Projects/release/adult-stream-app/src/app/page.tsx": "3", "/Users/<USER>/Projects/release/adult-stream-app/src/app/video/[id]/page.tsx": "4", "/Users/<USER>/Projects/release/adult-stream-app/src/components/layout/Footer.tsx": "5", "/Users/<USER>/Projects/release/adult-stream-app/src/components/layout/Header.tsx": "6", "/Users/<USER>/Projects/release/adult-stream-app/src/components/layout/MainLayout.tsx": "7", "/Users/<USER>/Projects/release/adult-stream-app/src/components/ui/Button.tsx": "8", "/Users/<USER>/Projects/release/adult-stream-app/src/components/ui/SearchBar.tsx": "9", "/Users/<USER>/Projects/release/adult-stream-app/src/components/ui/VideoCard.tsx": "10", "/Users/<USER>/Projects/release/adult-stream-app/src/lib/utils.ts": "11"}, {"size": 6595, "mtime": 1748412028277, "results": "12", "hashOfConfig": "13"}, {"size": 2339, "mtime": 1748411618619, "results": "14", "hashOfConfig": "13"}, {"size": 4455, "mtime": 1748411979663, "results": "15", "hashOfConfig": "13"}, {"size": 12770, "mtime": 1748412106387, "results": "16", "hashOfConfig": "13"}, {"size": 8976, "mtime": 1748411807662, "results": "17", "hashOfConfig": "13"}, {"size": 4999, "mtime": 1748411717030, "results": "18", "hashOfConfig": "13"}, {"size": 817, "mtime": 1748411821618, "results": "19", "hashOfConfig": "13"}, {"size": 2593, "mtime": 1748411643127, "results": "20", "hashOfConfig": "13"}, {"size": 4960, "mtime": 1748411749370, "results": "21", "hashOfConfig": "13"}, {"size": 5392, "mtime": 1748411855386, "results": "22", "hashOfConfig": "13"}, {"size": 2311, "mtime": 1748411662202, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "iqcyrg", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Projects/release/adult-stream-app/src/app/categories/page.tsx", ["57", "58"], [], "/Users/<USER>/Projects/release/adult-stream-app/src/app/layout.tsx", [], [], "/Users/<USER>/Projects/release/adult-stream-app/src/app/page.tsx", [], [], "/Users/<USER>/Projects/release/adult-stream-app/src/app/video/[id]/page.tsx", ["59"], [], "/Users/<USER>/Projects/release/adult-stream-app/src/components/layout/Footer.tsx", [], [], "/Users/<USER>/Projects/release/adult-stream-app/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Projects/release/adult-stream-app/src/components/layout/MainLayout.tsx", [], [], "/Users/<USER>/Projects/release/adult-stream-app/src/components/ui/Button.tsx", [], [], "/Users/<USER>/Projects/release/adult-stream-app/src/components/ui/SearchBar.tsx", ["60"], [], "/Users/<USER>/Projects/release/adult-stream-app/src/components/ui/VideoCard.tsx", [], [], "/Users/<USER>/Projects/release/adult-stream-app/src/lib/utils.ts", ["61", "62", "63", "64"], [], {"ruleId": "65", "severity": 2, "message": "66", "line": 7, "column": 10, "nodeType": null, "messageId": "67", "endLine": 7, "endColumn": 12}, {"ruleId": "68", "severity": 2, "message": "69", "line": 167, "column": 36, "nodeType": "70", "messageId": "71", "suggestions": "72"}, {"ruleId": "65", "severity": 2, "message": "73", "line": 68, "column": 9, "nodeType": null, "messageId": "67", "endLine": 68, "endColumn": 15}, {"ruleId": "65", "severity": 2, "message": "74", "line": 20, "column": 10, "nodeType": null, "messageId": "67", "endLine": 20, "endColumn": 19}, {"ruleId": "75", "severity": 2, "message": "76", "line": 50, "column": 46, "nodeType": "77", "messageId": "78", "endLine": 50, "endColumn": 49, "suggestions": "79"}, {"ruleId": "75", "severity": 2, "message": "76", "line": 50, "column": 56, "nodeType": "77", "messageId": "78", "endLine": 50, "endColumn": 59, "suggestions": "80"}, {"ruleId": "75", "severity": 2, "message": "76", "line": 61, "column": 46, "nodeType": "77", "messageId": "78", "endLine": 61, "endColumn": 49, "suggestions": "81"}, {"ruleId": "75", "severity": 2, "message": "76", "line": 61, "column": 56, "nodeType": "77", "messageId": "78", "endLine": 61, "endColumn": 59, "suggestions": "82"}, "@typescript-eslint/no-unused-vars", "'cn' is defined but never used.", "unusedVar", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["83", "84", "85", "86"], "'params' is assigned a value but never used.", "'isFocused' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["87", "88"], ["89", "90"], ["91", "92"], ["93", "94"], {"messageId": "95", "data": "96", "fix": "97", "desc": "98"}, {"messageId": "95", "data": "99", "fix": "100", "desc": "101"}, {"messageId": "95", "data": "102", "fix": "103", "desc": "104"}, {"messageId": "95", "data": "105", "fix": "106", "desc": "107"}, {"messageId": "108", "fix": "109", "desc": "110"}, {"messageId": "111", "fix": "112", "desc": "113"}, {"messageId": "108", "fix": "114", "desc": "110"}, {"messageId": "111", "fix": "115", "desc": "113"}, {"messageId": "108", "fix": "116", "desc": "110"}, {"messageId": "111", "fix": "117", "desc": "113"}, {"messageId": "108", "fix": "118", "desc": "110"}, {"messageId": "111", "fix": "119", "desc": "113"}, "replaceWithAlt", {"alt": "120"}, {"range": "121", "text": "122"}, "Replace with `&apos;`.", {"alt": "123"}, {"range": "124", "text": "125"}, "Replace with `&lsquo;`.", {"alt": "126"}, {"range": "127", "text": "128"}, "Replace with `&#39;`.", {"alt": "129"}, {"range": "130", "text": "131"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "132", "text": "133"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "134", "text": "135"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "136", "text": "133"}, {"range": "137", "text": "135"}, {"range": "138", "text": "133"}, {"range": "139", "text": "135"}, {"range": "140", "text": "133"}, {"range": "141", "text": "135"}, "&apos;", [5348, 5514], "\n              Discover content organized by your favorite categories. From amateur to professional, \n              find exactly what you&apos;re looking for.\n            ", "&lsquo;", [5348, 5514], "\n              Discover content organized by your favorite categories. From amateur to professional, \n              find exactly what you&lsquo;re looking for.\n            ", "&#39;", [5348, 5514], "\n              Discover content organized by your favorite categories. From amateur to professional, \n              find exactly what you&#39;re looking for.\n            ", "&rsquo;", [5348, 5514], "\n              Discover content organized by your favorite categories. From amateur to professional, \n              find exactly what you&rsquo;re looking for.\n            ", [1746, 1749], "unknown", [1746, 1749], "never", [1756, 1759], [1756, 1759], [2029, 2032], [2029, 2032], [2039, 2042], [2039, 2042]]