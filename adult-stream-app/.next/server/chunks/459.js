exports.id=459,exports.ids=[459],exports.modules={408:(e,t,r)=>{"use strict";r.d(t,{O:()=>u});var s=r(687),a=r(3210),l=r.n(a),i=r(5814),n=r.n(i),o=r(2643),c=r(6189),d=r(4780);function m({className:e,placeholder:t="Search videos...",onSearch:r}){let[l,i]=(0,a.useState)(""),[n,o]=(0,a.useState)(!1),[m,x]=(0,a.useState)([]),[h,u]=(0,a.useState)(!1),g=(0,a.useRef)(null),f=(0,c.useRouter)(),p=["amateur","blonde","brunette","milf","teen","asian","latina","big tits","pov","hardcore"];(0,d.sg)(e=>{e.length>0?(x(p.filter(t=>t.toLowerCase().includes(e.toLowerCase())).slice(0,5)),u(!0)):(x([]),u(!1))},300);let v=e=>{u(!1),r?r(e):f.push(`/search?q=${encodeURIComponent(e)}`)},b=e=>{i(e),v(e)};return(0,s.jsxs)("div",{className:(0,d.cn)("relative flex-col min-w-32 !h-10 max-w-xs",e),children:[(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),l.trim()&&v(l.trim())},className:"flex w-full flex-1 items-stretch rounded-lg h-full",children:[(0,s.jsx)("div",{className:"text-text-secondary flex border-none bg-background-input items-center justify-center pl-3 pr-2 rounded-l-lg border-r-0",children:(0,s.jsx)("svg",{fill:"currentColor",height:"20px",viewBox:"0 0 256 256",width:"20px",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"})})}),(0,s.jsx)("input",{ref:g,type:"text",value:l,onChange:e=>i(e.target.value),onFocus:()=>o(!0),onBlur:()=>{setTimeout(()=>u(!1),200)},onKeyDown:e=>{"Escape"===e.key&&(u(!1),g.current?.blur())},className:(0,d.cn)("form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-r-lg text-text","focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary","border-none bg-background-input h-full placeholder:text-text-placeholder","px-3 text-sm font-normal leading-normal transition-colors"),placeholder:t})]}),h&&m.length>0&&(0,s.jsx)("div",{className:"absolute top-full left-0 right-0 mt-1 bg-background-card border border-border rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto",children:m.map((e,t)=>(0,s.jsx)("button",{type:"button",className:"w-full text-left px-4 py-2 text-sm text-text hover:bg-background-hover transition-colors first:rounded-t-lg last:rounded-b-lg",onClick:()=>b(e),children:(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4 text-text-secondary",fill:"currentColor",viewBox:"0 0 256 256",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"})}),e]})},t))})]})}function x({className:e}){let[t,r]=(0,a.useState)(!1),l=[{name:"Home",href:"/"},{name:"Videos",href:"/videos"},{name:"Categories",href:"/categories"},{name:"Models",href:"/models"},{name:"Live",href:"/live"},{name:"Premium",href:"/premium"}];return(0,s.jsxs)("header",{className:(0,d.cn)("flex items-center justify-between whitespace-nowrap border-b border-solid border-border px-6 md:px-10 py-4",e),children:[(0,s.jsxs)("div",{className:"flex items-center gap-6 md:gap-8",children:[(0,s.jsxs)(n(),{href:"/",className:"flex items-center gap-2 text-text",children:[(0,s.jsx)("div",{className:"size-5 md:size-6 text-primary",children:(0,s.jsx)("svg",{fill:"none",viewBox:"0 0 48 48",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z",fill:"currentColor"})})}),(0,s.jsx)("h1",{className:"text-text text-xl md:text-2xl font-bold tracking-tight",children:"AdultStream"})]}),(0,s.jsx)("nav",{className:"hidden md:flex items-center gap-6",children:l.map(e=>(0,s.jsx)(n(),{href:e.href,className:"text-text hover:text-primary text-sm font-semibold leading-normal transition-colors",children:e.name},e.name))})]}),(0,s.jsxs)("div",{className:"flex flex-1 justify-end items-center gap-3 md:gap-4",children:[(0,s.jsx)("div",{className:"hidden sm:block",children:(0,s.jsx)(m,{})}),(0,s.jsx)(o.$,{variant:"ghost",size:"sm",className:"flex items-center justify-center rounded-lg h-10 w-10 bg-background-input hover:bg-background-hover text-text transition-colors","aria-label":"Notifications",children:(0,s.jsx)("svg",{fill:"currentColor",height:"20px",viewBox:"0 0 256 256",width:"20px",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z"})})}),(0,s.jsx)("div",{className:"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-primary cursor-pointer",style:{backgroundImage:'url("https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face")'}}),(0,s.jsx)(o.$,{variant:"ghost",size:"sm",className:"md:hidden text-text hover:text-primary p-2",onClick:()=>r(!t),"aria-label":"Toggle mobile menu",children:(0,s.jsx)("svg",{fill:"currentColor",height:"24px",viewBox:"0 0 256 256",width:"24px",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,88H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM40,184H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16Z"})})})]}),t&&(0,s.jsx)("div",{className:"absolute top-full left-0 right-0 bg-background-card border-b border-border md:hidden z-50",children:(0,s.jsxs)("div",{className:"px-6 py-4 space-y-4",children:[(0,s.jsx)(m,{}),(0,s.jsx)("nav",{className:"space-y-2",children:l.map(e=>(0,s.jsx)(n(),{href:e.href,className:"block text-text hover:text-primary text-sm font-semibold leading-normal transition-colors py-2",onClick:()=>r(!1),children:e.name},e.name))})]})})]})}function h({className:e}){let t=new Date().getFullYear(),r=[{name:"Terms of Service",href:"/terms"},{name:"Privacy Policy",href:"/privacy"},{name:"Contact Us",href:"/contact"},{name:"Help",href:"/help"},{name:"DMCA",href:"/dmca"}],a=[{name:"Twitter",href:"#",icon:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})})},{name:"Instagram",href:"#",icon:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.807-.875-1.297-2.026-1.297-3.323s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323z"})})},{name:"Discord",href:"#",icon:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0189 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z"})})}];return(0,s.jsx)("footer",{className:(0,d.cn)("border-t border-border py-8 px-6 md:px-10 bg-background-secondary",e),children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8",children:[(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,s.jsx)("div",{className:"size-6 text-primary",children:(0,s.jsx)("svg",{fill:"none",viewBox:"0 0 48 48",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z",fill:"currentColor"})})}),(0,s.jsx)("h2",{className:"text-text text-xl font-bold",children:"AdultStream"})]}),(0,s.jsx)("p",{className:"text-text-secondary text-sm leading-relaxed max-w-md",children:"Premium adult entertainment platform providing high-quality content with a focus on user experience and privacy. For adults 18+ only."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-text font-semibold mb-4",children:"Quick Links"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/categories",className:"text-text-secondary hover:text-primary text-sm transition-colors",children:"Browse Categories"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/models",className:"text-text-secondary hover:text-primary text-sm transition-colors",children:"Top Models"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/premium",className:"text-text-secondary hover:text-primary text-sm transition-colors",children:"Premium Content"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/upload",className:"text-text-secondary hover:text-primary text-sm transition-colors",children:"Upload Content"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-text font-semibold mb-4",children:"Support"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/help",className:"text-text-secondary hover:text-primary text-sm transition-colors",children:"Help Center"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/contact",className:"text-text-secondary hover:text-primary text-sm transition-colors",children:"Contact Support"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/report",className:"text-text-secondary hover:text-primary text-sm transition-colors",children:"Report Content"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/feedback",className:"text-text-secondary hover:text-primary text-sm transition-colors",children:"Send Feedback"})})]})]})]}),(0,s.jsx)("div",{className:"bg-background-input rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"text-warning flex-shrink-0 mt-0.5",children:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-text font-semibold text-sm mb-1",children:"Age Verification Required"}),(0,s.jsx)("p",{className:"text-text-secondary text-xs leading-relaxed",children:"This website contains adult content. By accessing this site, you confirm that you are 18 years or older and agree to our terms of service. All models are 18+ years old."})]})]})}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center pt-6 border-t border-border",children:[(0,s.jsxs)("p",{className:"text-text-secondary text-xs mb-4 md:mb-0",children:["\xa9 ",t," AdultStream. All rights reserved. For adults 18+ only."]}),(0,s.jsx)("div",{className:"flex flex-wrap items-center gap-4 mb-4 md:mb-0",children:r.map((e,t)=>(0,s.jsxs)(l().Fragment,{children:[(0,s.jsx)(n(),{href:e.href,className:"text-text-muted hover:text-text text-xs transition-colors",children:e.name}),t<r.length-1&&(0,s.jsx)("span",{className:"text-text-muted text-xs",children:"•"})]},e.name))}),(0,s.jsx)("div",{className:"flex items-center gap-3",children:a.map(e=>(0,s.jsx)(n(),{href:e.href,className:"text-text-secondary hover:text-primary transition-colors","aria-label":e.name,children:e.icon},e.name))})]})]})})}function u({children:e,className:t,showHeader:r=!0,showFooter:a=!0}){return(0,s.jsx)("div",{className:(0,d.cn)("relative flex size-full min-h-screen flex-col bg-background group/design-root overflow-x-hidden",t),children:(0,s.jsxs)("div",{className:"layout-container flex h-full grow flex-col",children:[r&&(0,s.jsx)(x,{}),(0,s.jsx)("main",{className:"flex flex-1 flex-col",children:e}),a&&(0,s.jsx)(h,{})]})})}},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1135:()=>{},2643:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var s=r(687),a=r(3210),l=r.n(a),i=r(4780);let n=l().forwardRef(({className:e,variant:t="primary",size:r="md",isLoading:a=!1,disabled:l,children:n,...o},c)=>(0,s.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary text-white hover:bg-primary-hover focus:ring-primary",secondary:"bg-background-input text-text hover:bg-background-hover focus:ring-background-input",outline:"border border-border text-text hover:bg-background-input focus:ring-border",ghost:"text-text hover:bg-background-input focus:ring-background-input",danger:"bg-error text-white hover:bg-red-600 focus:ring-error"}[t],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-sm",lg:"h-12 px-6 text-base",xl:"h-14 px-8 text-lg"}[r],a&&"cursor-not-allowed",e),disabled:l||a,ref:c,...o,children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):n}));n.displayName="Button"},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>o});var s=r(7413),a=r(2851),l=r.n(a),i=r(5544),n=r.n(i);r(1135);let o={title:"AdultStream - Premium Video Hub",description:"Discover premium adult content with AdultStream. Browse categories, watch videos, and enjoy a seamless streaming experience.",keywords:["adult","streaming","videos","entertainment","premium"],authors:[{name:"AdultStream"}],creator:"AdultStream",publisher:"AdultStream",robots:{index:!1,follow:!1},viewport:{width:"device-width",initialScale:1,maximumScale:1},themeColor:"#E92933",colorScheme:"dark",openGraph:{type:"website",locale:"en_US",url:"https://adultstream.com",siteName:"AdultStream",title:"AdultStream - Premium Video Hub",description:"Discover premium adult content with AdultStream. Browse categories, watch videos, and enjoy a seamless streaming experience.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"AdultStream"}]},twitter:{card:"summary_large_image",title:"AdultStream - Premium Video Hub",description:"Discover premium adult content with AdultStream. Browse categories, watch videos, and enjoy a seamless streaming experience.",images:["/og-image.jpg"]}};function c({children:e}){return(0,s.jsxs)("html",{lang:"en",className:"dark",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,s.jsx)("body",{className:`${l().variable} ${n().variable} bg-background text-text antialiased`,children:(0,s.jsx)("div",{className:"relative flex min-h-screen flex-col overflow-x-hidden",children:e})})]})}},4780:(e,t,r)=>{"use strict";r.d(t,{OO:()=>n,a3:()=>i,ak:()=>o,cn:()=>l,sg:()=>c});var s=r(9384),a=r(2348);function l(...e){return(0,a.QP)((0,s.$)(e))}function i(e){let t=Math.floor(e/60);return`${t}:${(e%60).toString().padStart(2,"0")}`}function n(e){return e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString()}function o(e){let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"Just now";if(t<3600){let e=Math.floor(t/60);return`${e} minute${e>1?"s":""} ago`}if(t<86400){let e=Math.floor(t/3600);return`${e} hour${e>1?"s":""} ago`}if(t<604800){let e=Math.floor(t/86400);return`${e} day${e>1?"s":""} ago`}if(t<2592e3){let e=Math.floor(t/604800);return`${e} week${e>1?"s":""} ago`}else if(t<31536e3){let e=Math.floor(t/2592e3);return`${e} month${e>1?"s":""} ago`}else{let e=Math.floor(t/31536e3);return`${e} year${e>1?"s":""} ago`}}function c(e,t){let r;return(...s)=>{clearTimeout(r),r=setTimeout(()=>e(...s),t)}}},5434:()=>{},6050:()=>{},9132:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9404:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))}};