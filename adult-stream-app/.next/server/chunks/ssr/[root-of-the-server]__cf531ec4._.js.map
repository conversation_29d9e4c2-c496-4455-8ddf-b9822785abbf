{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/app/categories/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\ninterface Category {\n  name: string;\n  image: string;\n}\n\nconst categories: Category[] = [\n  {\n    name: 'Big Tits',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDnVrvm1DwRW5Nfjjf3V27jy52hMYIGcco3ZlJtnPQO4XEmuPzX95VTiGpnpWNLgIDq-NBN8tfwwKs3GfJsNg9ykbfbVPEzy1sIbdTTFIQVLwM-Im9I40VWPNZMsjEqfLREM0ryz0zTxNKDEal-hBwuIoQtR73zy5F7Uu5gwwglcB9vZsoft22hhcTTCyliGOqmjenf6OFaiMEQyexn1cAsdeVyuO-u3Qjc5vyZKiOCq0tfDPB85WS36nkeiJ3H28nTJdyM82SEpV6x'\n  },\n  {\n    name: 'MILF',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCmjfZIDXtVPH_C-gs-SrE61OTbvUAGlwaJn6K3PV_CsY1hRwdMIKmdOZ4HKunOGhBtHrd-jH_mWiPJs-v0vmVH5WuiyoUiwNJ2QDTMYW6QXT19vVCPQX6A3l886Omv_CTKPtwIJe8Ajh7Hs_fxtXx5eVPRHQw7CdVbwHS4kjr5Fk28UGe7fCoZv4r01mnwsZJywEneQQIxww5VVyV6HP3ieQRGfCqwISJshl2fMdrrGxqpB3y1DUz448HIu5YNUryW8Zb9dWqydG5J'\n  },\n  {\n    name: 'POV',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDtxp-r8-nct5ErDgiC3Y9K_7Lfh3wC4iFWqvaendssdIIdjbOY2I2oRMZp8Iq20v4zadIclbMQmoFWiHAM6VI9fZWUScgCbPWVVfOKEJc-OH33-q0RRYj2iHbttz5-rWCQ-4x4ZrNv4MS_QixUIAEMICXz5Q7dcBrq4OtyUiD9dCybkNsFKlK_jDKw2aeA0DbXSK2uT6QKm9FdyrWKJjfh-qoAr5DaezpTaVSuSPmbJ3we5FldeCnwRvmZpzFjBa8yvpe_xvjaZ3kY'\n  },\n  {\n    name: 'Blonde',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBWhSOS9vn6_9_6ejf1HFOULifKtjb-NFozoa7WleoS9BRTEpXTKgt3W9Ea1BQLTFjGaHHM_Rp0BT3RApvbKTBLWeJ0PQ2_D-8V3oKKiALVQvRjrjztPVbtLkIxx5E-RHhvftLVi2uWO0aO-auPqWFXQnf4DsjWToNQ_dnekwCbssmkuOi2vEW_FaWPSsIn-tIbvkKXCerIfqvXxMFSXuGbqIAS89EEgFiYgg96k0s1TEmm6TTqbRa0KCGzQ5xTqZUfjMMHsv0OKKnt'\n  },\n  {\n    name: 'Brunette',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDYi3vd_czZqaYivg094RWWAgCePpYzid85bqtivFxlQM8XBRFQeQkKHnC5fG-TkUjcW-n1oru_RVUyF2PMPMJ7fBP9SFXvM0dW2KY-Ub4ziOQncBtktrWQh_wmQrXxzZSCFGVCBa0WzlRtoKjgzW_AMEL48WkPvqfrjv0PG3_pQC8apwhp9H-TMDU2PTVANYxrzgaVe39zwICHYlVd7dgav9TQTGjs8X9AmOcRhInTShad2rpa3ysDQjhf3gnIp5-7BdjKNb1VgCcT'\n  },\n  {\n    name: 'Redhead',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAwYqIvpZMNreOs2rxl7FDpHTo1jHTtaWMZ9xrhVQc7XEFsU_jldHjP7NjD682DYCKRM_zGH6PaLnHcjsS9GW-LOozqc6LU5cpu4eCU_6QtxdIaKSUyvK2n7jHjSVQhhEa4sFrf3-5dpcRUyYR-ZREoFvfFn2AqJZd4OzHckRVBQzzn5q6LdVAIz7Npb3CInyMgVAPcNN8e8QOruXo6m4qhCiivXfcjKxB7Ow0Z9qNfhsdXuvO62IBIzlD3-pL-0DLFmUb-Y9ZlDZrR'\n  },\n  {\n    name: 'Asian',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBkLz9rJ6E4jNXu9ooPNepwPnZO_N5GzQ8mNZIGCPw_0ejGE4i1_AhOIQ9sLZT86No-qx8DN4EGXk8DGvjZJwhfIK0FanNu-UZ0MlOCPwbzJ-rme_WK5ombatxOXm9J9j2wYfMTAvp8AmehaSHige6iCyyFyb9JqjgW6P_BKWjnXePo80jhsTUW6ZxFcAnxug6CTr4HmPE824Aje2kF9fRyKjG2jZWyj0MPCKMx4x-pqg5YCZ89Mne66TGMwGGNe9YVsNg1hAEL_0h9'\n  },\n  {\n    name: 'Black',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAKNscCiRR2AU5xuk7Sto_4jCpAWlAs8WVww2duHrGIZDciz9Vq6eUJ1T5xgUbzQ9ONv57gcI1LEIfjWCT-nQGxqTxkcdL3P6cQl6hYOmbEhEOimlLCaPLckHM2jnuvbVUjSuZbGUs15PUyefjz7t-bHnojwYp0TPDXah0FZwHWe0nhsmo2kEiC9fxmhzya3bQYRsLHMWw9v7Ugt4wCzdQXiclLdEbTEZ2s_odiZe-vj3pDG9MJ1gDCZ1l5ajMb3BN1c3L8ODsSLmqc'\n  },\n  {\n    name: 'Latina',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA7gY2rMtr_fSF1n2aeF1jWyPwpCas0JbNAeeZPrSQOcHxyZEjMs_U1yzSzHBzBMBq2vN4KKdcg2kujejDR2UAshHiTSdSiNJNB95FckaFVaU2wcH6BLqAG4u04KtAZY9R8ovvaHXftcVzxklRp8x2JgEJLvx37C6hIPZX0YZyg00sHsuOx65eOhHb9vz2XzeczA2oHR2lPc9yBTAfgxYF6JCBFLFdHgd-U6lbgugoL53Y85orncyjUvqRShrMRUnDwObdAH9lVrfNd'\n  },\n  {\n    name: 'Amateur',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBf2N2pLIYjyWHFQ6HGO97OAKvHNuktfcICn-4o6q2CRfU4D16K5tuwbrM7ySgZBMKnXlGWGOLGO0W4qLv-uNRhGKsdHvsvRQxBrACW4E152Lj3Dh-vykrTDm9Y7DtNwHptp3TyRQwqBKaZMSP4FmT99T2l1ay0SoQE24LT5Y85p3kpiF-o4-zQkHKoWFjQLAQctxRKgkJJcvhgUdR_pC3NEhRWwmHN_ogRpQA7tP5YagV0jr3hOE9XbAqaCPfZrTog0ihGFhfWRAba'\n  },\n  {\n    name: 'Teen',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA1DgnjUG2m78BS31Hh_p0_aEiNnUdgYeo3bAJvnDuadnvu1Mn-M7m9C84SIzavcd6jMIlC87tuCUvjAYE6s3kssrgx3vVWJafX1bzWjktOK9w4-dZ4fFuiELj7DzntR2T6L6AY3ak58qf5sTE0bRDUuS0ImhVGUAdYAT4JmImmwG8i9v7DCFj3_BtTneJdOHtGIDGt9M32j4L8vz65GXs1FAT6QG7Dem70WL-vPzr_GozEIW7H1dVr1shGYfJf5fHWCxO52znTuqk8'\n  },\n  {\n    name: 'Mature',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuB399n5jsMLZP3E521_95YGyUj-JYCbAJRoMTXx8YoEhJ47CYdy-td9lfDhgXJjosQbXvV9QDYTroRoSP9m_vV4ipPQIUyRVcK8gsN4nF7KibY6SnI9Qc7AxKL3hXpe_o505GQAjhX0wOo1bOqwx3k4BUtuFervKeoA-VWP-h_JW2jASYcbsQrGVTvZbqr4WgqerKhHvtbbDUrunP_PvHheg9m1bxvp77iCQlk2iWLruLxfpdS0ymglBjANZfdtU4kPS8TXqKP39K3B'\n  },\n  {\n    name: 'Fetish',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBf-LU36CTb5r5ts_sQCH41lTXcfGFz1ml_iNqKQQBoD0BtBb9QzRH8lTMFBiZdbZM9xjchoTVx24hBy0KVfPqj5lIvHCk_Qd4-DDj-W6s3SnbwXXaM7BohcTIElYOb9VZjBtsE3PX7Sok1ha7Ni-k8iLrR1kzragF5DwKmmopPgsY6NoODvRe4oTYd_7b6Xt0vxuxPMclfwZ0Lem-4Ky2UAykKc6FyAjRrSJPfuj5ADZcXPY_-3momBEh8N9chZ56kFoI7SYKcY69V'\n  },\n  {\n    name: 'Gay',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAxwTW-zXUzUx_Usv5ZRAgn6gW6ZcJsbbXR8Id4piLm4wEy3F6eilSkZnvQp2ImmqDGzxm9mtUjnION_jxCuXsNsaGCPxLptAdJ8H6guWmzhpvnhPbSf-rRlriQE5np_bqdW3BWwzm6zyKd0UUTWMYzSusBsPRfAUqojCD-AD2vVUMzYHVCC3tJannfVnSvEg65Co94-2zMysoXEPE_ZB88-9wkH5jmVcOpyRsT27V0GdcbTd6nYHlx3BXWmnylM2O0BzX-iU6iruSi'\n  },\n  {\n    name: 'Lesbian',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA7xQu0FWYiw_vrF5lwSn5UgxhSRYcJyZWisEsOqVlUPPmGd9oaMeS-S3_zq7nit3La7ZrZn4bDf6v_8K2IVfmB9lwVs7UQM7salTbgRHRO-eO9DKbeulQTlw7xa0y_M_gN7RRIpuwO81xAa3VP6ijxcbWASQM4uR6TxUi8BMqjIsi1Oi12hv9JI0wGeuPjSLyJXzs4zHIQQTW3dF0DrPYI29qJG6gkOwnAjtEXpFYe2FO5y4rqfKg3EhXSMSycfbTk8-NbHNgC7eHe'\n  },\n  {\n    name: 'Transgender',\n    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA8YwPHRUZNPcf1Ab6FrvfXWLLoiTeLzD-GpIsP8PPCz0dXO6G6NSXWSxox11bzLt-gwDPJieIeUFv0ZgkfPrRf5Yc-8KtUUFdzr3P1W998eWxSpptQ3XuSPYJEh9eEmOOST_NZyEJRsDy45fYmwwpiYwkjjSxr19tsOIZiON5_nPU4dbSAMKchh6EJl1NXMZ7QjSkckQSVje5Gp-Ve1C0IUonXe6-e0tMX63W7BPEypLAQJWRl_PyQfs7saMhy_27BXnrhl40k1PQ4'\n  }\n];\n\nexport default function CategoriesPage() {\n  return (\n    <html>\n      <head>\n        <meta charSet=\"utf-8\" />\n        <link crossOrigin=\"\" href=\"https://fonts.gstatic.com/\" rel=\"preconnect\" />\n        <link\n          as=\"style\"\n          href=\"https://fonts.googleapis.com/css2?display=swap&family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800\"\n          onLoad={(e) => { (e.target as HTMLLinkElement).rel = 'stylesheet'; }}\n          rel=\"stylesheet\"\n        />\n        <title>Stitch Design</title>\n        <link href=\"data:image/x-icon;base64,\" rel=\"icon\" type=\"image/x-icon\" />\n        <script src=\"https://cdn.tailwindcss.com?plugins=forms,container-queries\"></script>\n        <style dangerouslySetInnerHTML={{\n          __html: `\n            .category-card {\n              transition: transform 0.2s ease-in-out;\n            }\n\n            .category-card:hover {\n              transform: translateY(-4px);\n            }\n\n            .category-card:hover .category-image {\n              filter: brightness(1.1);\n            }\n\n            .category-card:hover .category-title {\n              color: #e92933;\n            }\n          `\n        }} />\n      </head>\n\n      <body className=\"bg-[#1A090A] dark\" style={{ fontFamily: '\"Plus Jakarta Sans\", \"Noto Sans\", sans-serif' }}>\n        <div className=\"relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden\">\n          <div className=\"layout-container flex h-full grow flex-col\">\n            <header className=\"flex items-center justify-between whitespace-nowrap border-b border-solid border-[#3A1A1C] px-6 sm:px-10 py-4\">\n              <div className=\"flex items-center gap-3 text-white\">\n                <div className=\"size-6 text-[#e92933]\">\n                  <svg fill=\"none\" viewBox=\"0 0 48 48\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path\n                      clipRule=\"evenodd\"\n                      d=\"M24 4H42V17.3333V30.6667H24V44H6V30.6667V17.3333H24V4Z\"\n                      fill=\"currentColor\"\n                      fillRule=\"evenodd\"\n                    />\n                  </svg>\n                </div>\n                <h2 className=\"text-xl font-bold leading-tight tracking-[-0.015em]\">Streamr</h2>\n              </div>\n              <div className=\"flex items-center gap-4\">\n                <button className=\"flex items-center justify-center rounded-lg h-10 w-10 bg-[#3A1A1C] text-white hover:bg-[#e92933] transition-colors\">\n                  <div className=\"text-white\" data-icon=\"List\" data-size=\"24px\" data-weight=\"regular\">\n                    <svg fill=\"currentColor\" height=\"24px\" viewBox=\"0 0 256 256\" width=\"24px\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,72H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM216,184H40a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Z\" />\n                    </svg>\n                  </div>\n                </button>\n                <div\n                  className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-[#e92933]\"\n                  style={{\n                    backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuD7lTVEcTFE2cKUsM6EnMAeFWsGJ9AHmOVMezqGpcfbD8ZHQnUvvZvLyAmgyku9IhPF1Ir_Mfty55o5P-cXxFVtyJsctN2Aqc8ZxNExnK3vbf6yi_Jei7zHvv_Jt4dY87tYnhCKZyAkvfXB9gcz8ayeYCarDhMP_Nq7mzf_le66b8hePH0iC5rZKNH4VUvTCx17Vw2OhRV_66IMS4HCnwXjGM9GQGe-MsBTRJEuO4aBVdolF0QCz_j2CvzZEorUsbK9JJWkh2Gd50KQ\")'\n                  }}\n                />\n              </div>\n            </header>\n            <main className=\"px-4 sm:px-8 md:px-16 lg:px-24 xl:px-40 flex flex-1 justify-center py-8\">\n              <div className=\"layout-content-container flex flex-col w-full max-w-screen-xl\">\n                <div className=\"flex flex-wrap justify-between items-center gap-4 p-4 mb-4\">\n                  <h1 className=\"text-white text-3xl sm:text-4xl font-bold leading-tight tracking-tight\">Categories</h1>\n                </div>\n                <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 sm:gap-6 p-4\">\n                  {categories.map((category, index) => (\n                    <Link key={index} className=\"flex flex-col gap-2 category-card group\" href=\"#\">\n                      <div\n                        className=\"w-full bg-center bg-no-repeat aspect-[3/4] bg-cover rounded-lg overflow-hidden category-image\"\n                        style={{ backgroundImage: `url(\"${category.image}\")` }}\n                      />\n                      <p className=\"text-white text-sm sm:text-base font-semibold leading-normal category-title\">\n                        {category.name}\n                      </p>\n                    </Link>\n                  ))}\n                </div>\n              </div>\n            </main>\n          </div>\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAUA,MAAM,aAAyB;IAC7B;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC;;kCACC,8OAAC;wBAAK,SAAQ;;;;;;kCACd,8OAAC;wBAAK,aAAY;wBAAG,MAAK;wBAA6B,KAAI;;;;;;kCAC3D,8OAAC;wBACC,IAAG;wBACH,MAAK;wBACL,QAAQ,CAAC;4BAAS,EAAE,MAAM,CAAqB,GAAG,GAAG;wBAAc;wBACnE,KAAI;;;;;;kCAEN,8OAAC;kCAAM;;;;;;kCACP,8OAAC;wBAAK,MAAK;wBAA4B,KAAI;wBAAO,MAAK;;;;;;kCACvD,8OAAC;wBAAO,KAAI;;;;;;kCACZ,8OAAC;wBAAM,yBAAyB;4BAC9B,QAAQ,CAAC;;;;;;;;;;;;;;;;UAgBT,CAAC;wBACH;;;;;;;;;;;;0BAGF,8OAAC;gBAAK,WAAU;gBAAoB,OAAO;oBAAE,YAAY;gBAA+C;0BACtG,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,MAAK;oDAAO,SAAQ;oDAAY,OAAM;8DACzC,cAAA,8OAAC;wDACC,UAAS;wDACT,GAAE;wDACF,MAAK;wDACL,UAAS;;;;;;;;;;;;;;;;0DAIf,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;;;;;;;kDAEtE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC;oDAAI,WAAU;oDAAa,aAAU;oDAAO,aAAU;oDAAO,eAAY;8DACxE,cAAA,8OAAC;wDAAI,MAAK;wDAAe,QAAO;wDAAO,SAAQ;wDAAc,OAAM;wDAAO,OAAM;kEAC9E,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;0DAId,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,iBAAiB;gDACnB;;;;;;;;;;;;;;;;;;0CAIN,8OAAC;gCAAK,WAAU;0CACd,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAyE;;;;;;;;;;;sDAEzF,8OAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC,4JAAA,CAAA,UAAI;oDAAa,WAAU;oDAA0C,MAAK;;sEACzE,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,CAAC,KAAK,EAAE,SAAS,KAAK,CAAC,EAAE,CAAC;4DAAC;;;;;;sEAEvD,8OAAC;4DAAE,WAAU;sEACV,SAAS,IAAI;;;;;;;mDANP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB/B", "debugId": null}}]}