module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/categories/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CategoriesPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
'use client';
;
;
const categories = [
    {
        name: 'Big Tits',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDnVrvm1DwRW5Nfjjf3V27jy52hMYIGcco3ZlJtnPQO4XEmuPzX95VTiGpnpWNLgIDq-NBN8tfwwKs3GfJsNg9ykbfbVPEzy1sIbdTTFIQVLwM-Im9I40VWPNZMsjEqfLREM0ryz0zTxNKDEal-hBwuIoQtR73zy5F7Uu5gwwglcB9vZsoft22hhcTTCyliGOqmjenf6OFaiMEQyexn1cAsdeVyuO-u3Qjc5vyZKiOCq0tfDPB85WS36nkeiJ3H28nTJdyM82SEpV6x'
    },
    {
        name: 'MILF',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCmjfZIDXtVPH_C-gs-SrE61OTbvUAGlwaJn6K3PV_CsY1hRwdMIKmdOZ4HKunOGhBtHrd-jH_mWiPJs-v0vmVH5WuiyoUiwNJ2QDTMYW6QXT19vVCPQX6A3l886Omv_CTKPtwIJe8Ajh7Hs_fxtXx5eVPRHQw7CdVbwHS4kjr5Fk28UGe7fCoZv4r01mnwsZJywEneQQIxww5VVyV6HP3ieQRGfCqwISJshl2fMdrrGxqpB3y1DUz448HIu5YNUryW8Zb9dWqydG5J'
    },
    {
        name: 'POV',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDtxp-r8-nct5ErDgiC3Y9K_7Lfh3wC4iFWqvaendssdIIdjbOY2I2oRMZp8Iq20v4zadIclbMQmoFWiHAM6VI9fZWUScgCbPWVVfOKEJc-OH33-q0RRYj2iHbttz5-rWCQ-4x4ZrNv4MS_QixUIAEMICXz5Q7dcBrq4OtyUiD9dCybkNsFKlK_jDKw2aeA0DbXSK2uT6QKm9FdyrWKJjfh-qoAr5DaezpTaVSuSPmbJ3we5FldeCnwRvmZpzFjBa8yvpe_xvjaZ3kY'
    },
    {
        name: 'Blonde',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBWhSOS9vn6_9_6ejf1HFOULifKtjb-NFozoa7WleoS9BRTEpXTKgt3W9Ea1BQLTFjGaHHM_Rp0BT3RApvbKTBLWeJ0PQ2_D-8V3oKKiALVQvRjrjztPVbtLkIxx5E-RHhvftLVi2uWO0aO-auPqWFXQnf4DsjWToNQ_dnekwCbssmkuOi2vEW_FaWPSsIn-tIbvkKXCerIfqvXxMFSXuGbqIAS89EEgFiYgg96k0s1TEmm6TTqbRa0KCGzQ5xTqZUfjMMHsv0OKKnt'
    },
    {
        name: 'Brunette',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDYi3vd_czZqaYivg094RWWAgCePpYzid85bqtivFxlQM8XBRFQeQkKHnC5fG-TkUjcW-n1oru_RVUyF2PMPMJ7fBP9SFXvM0dW2KY-Ub4ziOQncBtktrWQh_wmQrXxzZSCFGVCBa0WzlRtoKjgzW_AMEL48WkPvqfrjv0PG3_pQC8apwhp9H-TMDU2PTVANYxrzgaVe39zwICHYlVd7dgav9TQTGjs8X9AmOcRhInTShad2rpa3ysDQjhf3gnIp5-7BdjKNb1VgCcT'
    },
    {
        name: 'Redhead',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAwYqIvpZMNreOs2rxl7FDpHTo1jHTtaWMZ9xrhVQc7XEFsU_jldHjP7NjD682DYCKRM_zGH6PaLnHcjsS9GW-LOozqc6LU5cpu4eCU_6QtxdIaKSUyvK2n7jHjSVQhhEa4sFrf3-5dpcRUyYR-ZREoFvfFn2AqJZd4OzHckRVBQzzn5q6LdVAIz7Npb3CInyMgVAPcNN8e8QOruXo6m4qhCiivXfcjKxB7Ow0Z9qNfhsdXuvO62IBIzlD3-pL-0DLFmUb-Y9ZlDZrR'
    },
    {
        name: 'Asian',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBkLz9rJ6E4jNXu9ooPNepwPnZO_N5GzQ8mNZIGCPw_0ejGE4i1_AhOIQ9sLZT86No-qx8DN4EGXk8DGvjZJwhfIK0FanNu-UZ0MlOCPwbzJ-rme_WK5ombatxOXm9J9j2wYfMTAvp8AmehaSHige6iCyyFyb9JqjgW6P_BKWjnXePo80jhsTUW6ZxFcAnxug6CTr4HmPE824Aje2kF9fRyKjG2jZWyj0MPCKMx4x-pqg5YCZ89Mne66TGMwGGNe9YVsNg1hAEL_0h9'
    },
    {
        name: 'Black',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAKNscCiRR2AU5xuk7Sto_4jCpAWlAs8WVww2duHrGIZDciz9Vq6eUJ1T5xgUbzQ9ONv57gcI1LEIfjWCT-nQGxqTxkcdL3P6cQl6hYOmbEhEOimlLCaPLckHM2jnuvbVUjSuZbGUs15PUyefjz7t-bHnojwYp0TPDXah0FZwHWe0nhsmo2kEiC9fxmhzya3bQYRsLHMWw9v7Ugt4wCzdQXiclLdEbTEZ2s_odiZe-vj3pDG9MJ1gDCZ1l5ajMb3BN1c3L8ODsSLmqc'
    },
    {
        name: 'Latina',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA7gY2rMtr_fSF1n2aeF1jWyPwpCas0JbNAeeZPrSQOcHxyZEjMs_U1yzSzHBzBMBq2vN4KKdcg2kujejDR2UAshHiTSdSiNJNB95FckaFVaU2wcH6BLqAG4u04KtAZY9R8ovvaHXftcVzxklRp8x2JgEJLvx37C6hIPZX0YZyg00sHsuOx65eOhHb9vz2XzeczA2oHR2lPc9yBTAfgxYF6JCBFLFdHgd-U6lbgugoL53Y85orncyjUvqRShrMRUnDwObdAH9lVrfNd'
    },
    {
        name: 'Amateur',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBf2N2pLIYjyWHFQ6HGO97OAKvHNuktfcICn-4o6q2CRfU4D16K5tuwbrM7ySgZBMKnXlGWGOLGO0W4qLv-uNRhGKsdHvsvRQxBrACW4E152Lj3Dh-vykrTDm9Y7DtNwHptp3TyRQwqBKaZMSP4FmT99T2l1ay0SoQE24LT5Y85p3kpiF-o4-zQkHKoWFjQLAQctxRKgkJJcvhgUdR_pC3NEhRWwmHN_ogRpQA7tP5YagV0jr3hOE9XbAqaCPfZrTog0ihGFhfWRAba'
    },
    {
        name: 'Teen',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA1DgnjUG2m78BS31Hh_p0_aEiNnUdgYeo3bAJvnDuadnvu1Mn-M7m9C84SIzavcd6jMIlC87tuCUvjAYE6s3kssrgx3vVWJafX1bzWjktOK9w4-dZ4fFuiELj7DzntR2T6L6AY3ak58qf5sTE0bRDUuS0ImhVGUAdYAT4JmImmwG8i9v7DCFj3_BtTneJdOHtGIDGt9M32j4L8vz65GXs1FAT6QG7Dem70WL-vPzr_GozEIW7H1dVr1shGYfJf5fHWCxO52znTuqk8'
    },
    {
        name: 'Mature',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuB399n5jsMLZP3E521_95YGyUj-JYCbAJRoMTXx8YoEhJ47CYdy-td9lfDhgXJjosQbXvV9QDYTroRoSP9m_vV4ipPQIUyRVcK8gsN4nF7KibY6SnI9Qc7AxKL3hXpe_o505GQAjhX0wOo1bOqwx3k4BUtuFervKeoA-VWP-h_JW2jASYcbsQrGVTvZbqr4WgqerKhHvtbbDUrunP_PvHheg9m1bxvp77iCQlk2iWLruLxfpdS0ymglBjANZfdtU4kPS8TXqKP39K3B'
    },
    {
        name: 'Fetish',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBf-LU36CTb5r5ts_sQCH41lTXcfGFz1ml_iNqKQQBoD0BtBb9QzRH8lTMFBiZdbZM9xjchoTVx24hBy0KVfPqj5lIvHCk_Qd4-DDj-W6s3SnbwXXaM7BohcTIElYOb9VZjBtsE3PX7Sok1ha7Ni-k8iLrR1kzragF5DwKmmopPgsY6NoODvRe4oTYd_7b6Xt0vxuxPMclfwZ0Lem-4Ky2UAykKc6FyAjRrSJPfuj5ADZcXPY_-3momBEh8N9chZ56kFoI7SYKcY69V'
    },
    {
        name: 'Gay',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAxwTW-zXUzUx_Usv5ZRAgn6gW6ZcJsbbXR8Id4piLm4wEy3F6eilSkZnvQp2ImmqDGzxm9mtUjnION_jxCuXsNsaGCPxLptAdJ8H6guWmzhpvnhPbSf-rRlriQE5np_bqdW3BWwzm6zyKd0UUTWMYzSusBsPRfAUqojCD-AD2vVUMzYHVCC3tJannfVnSvEg65Co94-2zMysoXEPE_ZB88-9wkH5jmVcOpyRsT27V0GdcbTd6nYHlx3BXWmnylM2O0BzX-iU6iruSi'
    },
    {
        name: 'Lesbian',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA7xQu0FWYiw_vrF5lwSn5UgxhSRYcJyZWisEsOqVlUPPmGd9oaMeS-S3_zq7nit3La7ZrZn4bDf6v_8K2IVfmB9lwVs7UQM7salTbgRHRO-eO9DKbeulQTlw7xa0y_M_gN7RRIpuwO81xAa3VP6ijxcbWASQM4uR6TxUi8BMqjIsi1Oi12hv9JI0wGeuPjSLyJXzs4zHIQQTW3dF0DrPYI29qJG6gkOwnAjtEXpFYe2FO5y4rqfKg3EhXSMSycfbTk8-NbHNgC7eHe'
    },
    {
        name: 'Transgender',
        image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA8YwPHRUZNPcf1Ab6FrvfXWLLoiTeLzD-GpIsP8PPCz0dXO6G6NSXWSxox11bzLt-gwDPJieIeUFv0ZgkfPrRf5Yc-8KtUUFdzr3P1W998eWxSpptQ3XuSPYJEh9eEmOOST_NZyEJRsDy45fYmwwpiYwkjjSxr19tsOIZiON5_nPU4dbSAMKchh6EJl1NXMZ7QjSkckQSVje5Gp-Ve1C0IUonXe6-e0tMX63W7BPEypLAQJWRl_PyQfs7saMhy_27BXnrhl40k1PQ4'
    }
];
function CategoriesPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("head", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        charSet: "utf-8"
                    }, void 0, false, {
                        fileName: "[project]/src/app/categories/page.tsx",
                        lineNumber: 82,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        crossOrigin: "",
                        href: "https://fonts.gstatic.com/",
                        rel: "preconnect"
                    }, void 0, false, {
                        fileName: "[project]/src/app/categories/page.tsx",
                        lineNumber: 83,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        as: "style",
                        href: "https://fonts.googleapis.com/css2?display=swap&family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800",
                        onLoad: (e)=>{
                            e.target.rel = 'stylesheet';
                        },
                        rel: "stylesheet"
                    }, void 0, false, {
                        fileName: "[project]/src/app/categories/page.tsx",
                        lineNumber: 84,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("title", {
                        children: "Stitch Design"
                    }, void 0, false, {
                        fileName: "[project]/src/app/categories/page.tsx",
                        lineNumber: 90,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        href: "data:image/x-icon;base64,",
                        rel: "icon",
                        type: "image/x-icon"
                    }, void 0, false, {
                        fileName: "[project]/src/app/categories/page.tsx",
                        lineNumber: 91,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                        src: "https://cdn.tailwindcss.com?plugins=forms,container-queries"
                    }, void 0, false, {
                        fileName: "[project]/src/app/categories/page.tsx",
                        lineNumber: 92,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("style", {
                        dangerouslySetInnerHTML: {
                            __html: `
            .category-card {
              transition: transform 0.2s ease-in-out;
            }

            .category-card:hover {
              transform: translateY(-4px);
            }

            .category-card:hover .category-image {
              filter: brightness(1.1);
            }

            .category-card:hover .category-title {
              color: #e92933;
            }
          `
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/app/categories/page.tsx",
                        lineNumber: 93,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/categories/page.tsx",
                lineNumber: 81,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                className: "bg-[#1A090A] dark",
                style: {
                    fontFamily: '"Plus Jakarta Sans", "Noto Sans", sans-serif'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "layout-container flex h-full grow flex-col",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                                className: "flex items-center justify-between whitespace-nowrap border-b border-solid border-[#3A1A1C] px-6 sm:px-10 py-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-3 text-white",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "size-6 text-[#e92933]",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                    fill: "none",
                                                    viewBox: "0 0 48 48",
                                                    xmlns: "http://www.w3.org/2000/svg",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                        clipRule: "evenodd",
                                                        d: "M24 4H42V17.3333V30.6667H24V44H6V30.6667V17.3333H24V4Z",
                                                        fill: "currentColor",
                                                        fillRule: "evenodd"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/categories/page.tsx",
                                                        lineNumber: 121,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/categories/page.tsx",
                                                    lineNumber: 120,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/categories/page.tsx",
                                                lineNumber: 119,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                className: "text-xl font-bold leading-tight tracking-[-0.015em]",
                                                children: "Streamr"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/categories/page.tsx",
                                                lineNumber: 129,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/categories/page.tsx",
                                        lineNumber: 118,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "flex items-center justify-center rounded-lg h-10 w-10 bg-[#3A1A1C] text-white hover:bg-[#e92933] transition-colors",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-white",
                                                    "data-icon": "List",
                                                    "data-size": "24px",
                                                    "data-weight": "regular",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        fill: "currentColor",
                                                        height: "24px",
                                                        viewBox: "0 0 256 256",
                                                        width: "24px",
                                                        xmlns: "http://www.w3.org/2000/svg",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                            d: "M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,72H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM216,184H40a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Z"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/categories/page.tsx",
                                                            lineNumber: 135,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/categories/page.tsx",
                                                        lineNumber: 134,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/categories/page.tsx",
                                                    lineNumber: 133,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/categories/page.tsx",
                                                lineNumber: 132,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-[#e92933]",
                                                style: {
                                                    backgroundImage: 'url("https://lh3.googleusercontent.com/aida-public/AB6AXuD7lTVEcTFE2cKUsM6EnMAeFWsGJ9AHmOVMezqGpcfbD8ZHQnUvvZvLyAmgyku9IhPF1Ir_Mfty55o5P-cXxFVtyJsctN2Aqc8ZxNExnK3vbf6yi_Jei7zHvv_Jt4dY87tYnhCKZyAkvfXB9gcz8ayeYCarDhMP_Nq7mzf_le66b8hePH0iC5rZKNH4VUvTCx17Vw2OhRV_66IMS4HCnwXjGM9GQGe-MsBTRJEuO4aBVdolF0QCz_j2CvzZEorUsbK9JJWkh2Gd50KQ")'
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/categories/page.tsx",
                                                lineNumber: 139,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/categories/page.tsx",
                                        lineNumber: 131,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/categories/page.tsx",
                                lineNumber: 117,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                                className: "px-4 sm:px-8 md:px-16 lg:px-24 xl:px-40 flex flex-1 justify-center py-8",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "layout-content-container flex flex-col w-full max-w-screen-xl",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-wrap justify-between items-center gap-4 p-4 mb-4",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: "text-white text-3xl sm:text-4xl font-bold leading-tight tracking-tight",
                                                children: "Categories"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/categories/page.tsx",
                                                lineNumber: 150,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/categories/page.tsx",
                                            lineNumber: 149,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 sm:gap-6 p-4",
                                            children: categories.map((category, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    className: "flex flex-col gap-2 category-card group",
                                                    href: "#",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "w-full bg-center bg-no-repeat aspect-[3/4] bg-cover rounded-lg overflow-hidden category-image",
                                                            style: {
                                                                backgroundImage: `url("${category.image}")`
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/categories/page.tsx",
                                                            lineNumber: 155,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-white text-sm sm:text-base font-semibold leading-normal category-title",
                                                            children: category.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/categories/page.tsx",
                                                            lineNumber: 159,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, index, true, {
                                                    fileName: "[project]/src/app/categories/page.tsx",
                                                    lineNumber: 154,
                                                    columnNumber: 21
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/categories/page.tsx",
                                            lineNumber: 152,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/categories/page.tsx",
                                    lineNumber: 148,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/categories/page.tsx",
                                lineNumber: 147,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/categories/page.tsx",
                        lineNumber: 116,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/categories/page.tsx",
                    lineNumber: 115,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/categories/page.tsx",
                lineNumber: 114,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/categories/page.tsx",
        lineNumber: 80,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__cf531ec4._.js.map