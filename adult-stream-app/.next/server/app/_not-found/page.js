(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1891:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>m,routeModule:()=>c,tree:()=>l});var i=r(5239),s=r(8088),n=r(8170),o=r.n(n),a=r(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/Projects/release/adult-stream-app/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,m=[],u={require:r,loadChunk:()=>Promise.resolve()},c=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>d});var i=r(7413),s=r(2851),n=r.n(s),o=r(5544),a=r.n(o);r(1135);let d={title:"AdultStream - Premium Video Hub",description:"Discover premium adult content with AdultStream. Browse categories, watch videos, and enjoy a seamless streaming experience.",keywords:["adult","streaming","videos","entertainment","premium"],authors:[{name:"AdultStream"}],creator:"AdultStream",publisher:"AdultStream",robots:{index:!1,follow:!1},viewport:{width:"device-width",initialScale:1,maximumScale:1},themeColor:"#E92933",colorScheme:"dark",openGraph:{type:"website",locale:"en_US",url:"https://adultstream.com",siteName:"AdultStream",title:"AdultStream - Premium Video Hub",description:"Discover premium adult content with AdultStream. Browse categories, watch videos, and enjoy a seamless streaming experience.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"AdultStream"}]},twitter:{card:"summary_large_image",title:"AdultStream - Premium Video Hub",description:"Discover premium adult content with AdultStream. Browse categories, watch videos, and enjoy a seamless streaming experience.",images:["/og-image.jpg"]}};function l({children:e}){return(0,i.jsxs)("html",{lang:"en",className:"dark",children:[(0,i.jsxs)("head",{children:[(0,i.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),(0,i.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,i.jsx)("body",{className:`${n().variable} ${a().variable} bg-background text-text antialiased`,children:(0,i.jsx)("div",{className:"relative flex min-h-screen flex-col overflow-x-hidden",children:e})})]})}},5434:()=>{},6050:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9132:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9404:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,876],()=>r(1891));module.exports=i})();