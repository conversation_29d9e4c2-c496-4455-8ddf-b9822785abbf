{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDuration(seconds: number): string {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n}\n\nexport function formatViews(views: number): string {\n  if (views >= 1000000) {\n    return `${(views / 1000000).toFixed(1)}M`;\n  } else if (views >= 1000) {\n    return `${(views / 1000).toFixed(1)}K`;\n  }\n  return views.toString();\n}\n\nexport function formatTimeAgo(date: Date): string {\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n  \n  if (diffInSeconds < 60) {\n    return 'Just now';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 604800) {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days} day${days > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 2592000) {\n    const weeks = Math.floor(diffInSeconds / 604800);\n    return `${weeks} week${weeks > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 31536000) {\n    const months = Math.floor(diffInSeconds / 2592000);\n    return `${months} month${months > 1 ? 's' : ''} ago`;\n  } else {\n    const years = Math.floor(diffInSeconds / 31536000);\n    return `${years} year${years > 1 ? 's' : ''} ago`;\n  }\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,UAAU;IACnC,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI,SAAS,SAAS;QACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC3C,OAAO,IAAI,SAAS,MAAM;QACxB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACxC;IACA,OAAO,MAAM,QAAQ;AACvB;AAEO,SAAS,cAAc,IAAU;IACtC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;IACzD,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO,IAAI,gBAAgB,QAAQ;QACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;IAChD,OAAO,IAAI,gBAAgB,SAAS;QAClC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO,IAAI,gBAAgB,UAAU;QACnC,MAAM,SAAS,KAAK,KAAK,CAAC,gBAAgB;QAC1C,OAAO,GAAG,OAAO,MAAM,EAAE,SAAS,IAAI,MAAM,GAAG,IAAI,CAAC;IACtD,OAAO;QACL,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD;AACF;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  isLoading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant = 'primary', \n    size = 'md', \n    isLoading = false, \n    disabled,\n    children, \n    ...props \n  }, ref) => {\n    const baseStyles = \"inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background disabled:opacity-50 disabled:cursor-not-allowed\";\n    \n    const variants = {\n      primary: \"bg-primary text-white hover:bg-primary-hover focus:ring-primary\",\n      secondary: \"bg-background-input text-text hover:bg-background-hover focus:ring-background-input\",\n      outline: \"border border-border text-text hover:bg-background-input focus:ring-border\",\n      ghost: \"text-text hover:bg-background-input focus:ring-background-input\",\n      danger: \"bg-error text-white hover:bg-red-600 focus:ring-error\",\n    };\n    \n    const sizes = {\n      sm: \"h-8 px-3 text-sm\",\n      md: \"h-10 px-4 text-sm\",\n      lg: \"h-12 px-6 text-base\",\n      xl: \"h-14 px-8 text-lg\",\n    };\n\n    return (\n      <button\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          isLoading && \"cursor-not-allowed\",\n          className\n        )}\n        disabled={disabled || isLoading}\n        ref={ref}\n        {...props}\n      >\n        {isLoading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            Loading...\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport { Button };\nexport type { ButtonProps };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX,aAAa,sBACb;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;kBAER,0BACC;;8BACE,6LAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;;sCAER,6LAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAEA;;2BAIR;;;;;;AAIR;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/ui/SearchBar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport { debounce } from '@/lib/utils';\n\ninterface SearchBarProps {\n  className?: string;\n  placeholder?: string;\n  onSearch?: (query: string) => void;\n}\n\nexport function SearchBar({ \n  className, \n  placeholder = \"Search videos...\", \n  onSearch \n}: SearchBarProps) {\n  const [query, setQuery] = useState('');\n  const [isFocused, setIsFocused] = useState(false);\n  const [suggestions, setSuggestions] = useState<string[]>([]);\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const router = useRouter();\n\n  // Mock suggestions - in a real app, this would come from an API\n  const mockSuggestions = [\n    'amateur',\n    'blonde',\n    'brunette',\n    'milf',\n    'teen',\n    'asian',\n    'latina',\n    'big tits',\n    'pov',\n    'hardcore',\n  ];\n\n  // Debounced search function\n  const debouncedSearch = debounce((searchQuery: string) => {\n    if (searchQuery.length > 0) {\n      const filtered = mockSuggestions.filter(suggestion =>\n        suggestion.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n      setSuggestions(filtered.slice(0, 5));\n      setShowSuggestions(true);\n    } else {\n      setSuggestions([]);\n      setShowSuggestions(false);\n    }\n  }, 300);\n\n  useEffect(() => {\n    debouncedSearch(query);\n  }, [query, debouncedSearch]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (query.trim()) {\n      handleSearch(query.trim());\n    }\n  };\n\n  const handleSearch = (searchQuery: string) => {\n    setShowSuggestions(false);\n    if (onSearch) {\n      onSearch(searchQuery);\n    } else {\n      router.push(`/search?q=${encodeURIComponent(searchQuery)}`);\n    }\n  };\n\n  const handleSuggestionClick = (suggestion: string) => {\n    setQuery(suggestion);\n    handleSearch(suggestion);\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      setShowSuggestions(false);\n      inputRef.current?.blur();\n    }\n  };\n\n  return (\n    <div className={cn(\"relative flex-col min-w-32 !h-10 max-w-xs\", className)}>\n      <form onSubmit={handleSubmit} className=\"flex w-full flex-1 items-stretch rounded-lg h-full\">\n        {/* Search Icon */}\n        <div className=\"text-text-secondary flex border-none bg-background-input items-center justify-center pl-3 pr-2 rounded-l-lg border-r-0\">\n          <svg\n            fill=\"currentColor\"\n            height=\"20px\"\n            viewBox=\"0 0 256 256\"\n            width=\"20px\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path d=\"M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z\" />\n          </svg>\n        </div>\n\n        {/* Input Field */}\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          onFocus={() => setIsFocused(true)}\n          onBlur={() => {\n            // Delay hiding suggestions to allow clicking on them\n            setTimeout(() => setShowSuggestions(false), 200);\n          }}\n          onKeyDown={handleKeyDown}\n          className={cn(\n            \"form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-r-lg text-text\",\n            \"focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\",\n            \"border-none bg-background-input h-full placeholder:text-text-placeholder\",\n            \"px-3 text-sm font-normal leading-normal transition-colors\"\n          )}\n          placeholder={placeholder}\n        />\n      </form>\n\n      {/* Search Suggestions */}\n      {showSuggestions && suggestions.length > 0 && (\n        <div className=\"absolute top-full left-0 right-0 mt-1 bg-background-card border border-border rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto\">\n          {suggestions.map((suggestion, index) => (\n            <button\n              key={index}\n              type=\"button\"\n              className=\"w-full text-left px-4 py-2 text-sm text-text hover:bg-background-hover transition-colors first:rounded-t-lg last:rounded-b-lg\"\n              onClick={() => handleSuggestionClick(suggestion)}\n            >\n              <span className=\"flex items-center gap-2\">\n                <svg\n                  className=\"w-4 h-4 text-text-secondary\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 256 256\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                >\n                  <path d=\"M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z\" />\n                </svg>\n                {suggestion}\n              </span>\n            </button>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;;AAaO,SAAS,UAAU,EACxB,SAAS,EACT,cAAc,kBAAkB,EAChC,QAAQ,EACO;;IACf,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,gEAAgE;IAChE,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,4BAA4B;IAC5B,MAAM,kBAAkB,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QAChC,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,MAAM,WAAW,gBAAgB,MAAM,CAAC,CAAA,aACtC,WAAW,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;YAE3D,eAAe,SAAS,KAAK,CAAC,GAAG;YACjC,mBAAmB;QACrB,OAAO;YACL,eAAe,EAAE;YACjB,mBAAmB;QACrB;IACF,GAAG;IAEH,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,gBAAgB;QAClB;8BAAG;QAAC;QAAO;KAAgB;IAE3B,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,MAAM,IAAI,IAAI;YAChB,aAAa,MAAM,IAAI;QACzB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,mBAAmB;QACnB,IAAI,UAAU;YACZ,SAAS;QACX,OAAO;YACL,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,cAAc;QAC5D;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,SAAS;QACT,aAAa;IACf;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB,mBAAmB;YACnB,SAAS,OAAO,EAAE;QACpB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;0BAC9D,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,OAAM;4BACN,OAAM;sCAEN,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,SAAS,IAAM,aAAa;wBAC5B,QAAQ;4BACN,qDAAqD;4BACrD,WAAW,IAAM,mBAAmB,QAAQ;wBAC9C;wBACA,WAAW;wBACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA,2EACA,4EACA;wBAEF,aAAa;;;;;;;;;;;;YAKhB,mBAAmB,YAAY,MAAM,GAAG,mBACvC,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;wBAEC,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,sBAAsB;kCAErC,cAAA,6LAAC;4BAAK,WAAU;;8CACd,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;oCACR,OAAM;8CAEN,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;gCAET;;;;;;;uBAdE;;;;;;;;;;;;;;;;AAsBnB;GAzIgB;;QAUC,qIAAA,CAAA,YAAS;;;KAVV", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/Button';\nimport { SearchBar } from '@/components/ui/SearchBar';\nimport { cn } from '@/lib/utils';\n\ninterface HeaderProps {\n  className?: string;\n}\n\nexport function Header({ className }: HeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'Videos', href: '/videos' },\n    { name: 'Categories', href: '/categories' },\n    { name: 'Models', href: '/models' },\n    { name: 'Live', href: '/live' },\n    { name: 'Premium', href: '/premium' },\n  ];\n\n  return (\n    <header className={cn(\n      \"flex items-center justify-between whitespace-nowrap border-b border-solid border-border px-6 md:px-10 py-4\",\n      className\n    )}>\n      {/* Logo and Navigation */}\n      <div className=\"flex items-center gap-6 md:gap-8\">\n        {/* Logo */}\n        <Link href=\"/\" className=\"flex items-center gap-2 text-text\">\n          <div className=\"size-5 md:size-6 text-primary\">\n            <svg fill=\"none\" viewBox=\"0 0 48 48\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path\n                d=\"M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z\"\n                fill=\"currentColor\"\n              />\n            </svg>\n          </div>\n          <h1 className=\"text-text text-xl md:text-2xl font-bold tracking-tight\">\n            AdultStream\n          </h1>\n        </Link>\n\n        {/* Desktop Navigation */}\n        <nav className=\"hidden md:flex items-center gap-6\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className=\"text-text hover:text-primary text-sm font-semibold leading-normal transition-colors\"\n            >\n              {item.name}\n            </Link>\n          ))}\n        </nav>\n      </div>\n\n      {/* Right Side Actions */}\n      <div className=\"flex flex-1 justify-end items-center gap-3 md:gap-4\">\n        {/* Search Bar - Hidden on mobile */}\n        <div className=\"hidden sm:block\">\n          <SearchBar />\n        </div>\n\n        {/* Notifications */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"flex items-center justify-center rounded-lg h-10 w-10 bg-background-input hover:bg-background-hover text-text transition-colors\"\n          aria-label=\"Notifications\"\n        >\n          <svg\n            fill=\"currentColor\"\n            height=\"20px\"\n            viewBox=\"0 0 256 256\"\n            width=\"20px\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path d=\"M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z\" />\n          </svg>\n        </Button>\n\n        {/* User Avatar */}\n        <div\n          className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-primary cursor-pointer\"\n          style={{\n            backgroundImage: `url(\"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face\")`,\n          }}\n        />\n\n        {/* Mobile Menu Button */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"md:hidden text-text hover:text-primary p-2\"\n          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          aria-label=\"Toggle mobile menu\"\n        >\n          <svg\n            fill=\"currentColor\"\n            height=\"24px\"\n            viewBox=\"0 0 256 256\"\n            width=\"24px\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path d=\"M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,88H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM40,184H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16Z\" />\n          </svg>\n        </Button>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMobileMenuOpen && (\n        <div className=\"absolute top-full left-0 right-0 bg-background-card border-b border-border md:hidden z-50\">\n          <div className=\"px-6 py-4 space-y-4\">\n            {/* Mobile Search */}\n            <SearchBar />\n            \n            {/* Mobile Navigation */}\n            <nav className=\"space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block text-text hover:text-primary text-sm font-semibold leading-normal transition-colors py-2\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,EAAE,SAAS,EAAe;;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,8GACA;;0BAGA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,MAAK;oCAAO,SAAQ;oCAAY,OAAM;8CACzC,cAAA,6LAAC;wCACC,GAAE;wCACF,MAAK;;;;;;;;;;;;;;;;0CAIX,6LAAC;gCAAG,WAAU;0CAAyD;;;;;;;;;;;;kCAMzE,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;0CAET,KAAK,IAAI;+BAJL,KAAK,IAAI;;;;;;;;;;;;;;;;0BAWtB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,wIAAA,CAAA,YAAS;;;;;;;;;;kCAIZ,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC;4BACC,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,OAAM;4BACN,OAAM;sCAEN,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,CAAC,gGAAgG,CAAC;wBACrH;;;;;;kCAIF,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,oBAAoB,CAAC;wBACpC,cAAW;kCAEX,cAAA,6LAAC;4BACC,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,OAAM;4BACN,OAAM;sCAEN,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;YAMb,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,wIAAA,CAAA,YAAS;;;;;sCAGV,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAElC,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC;GA9HgB;KAAA", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\n\ninterface FooterProps {\n  className?: string;\n}\n\nexport function Footer({ className }: FooterProps) {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = [\n    { name: 'Terms of Service', href: '/terms' },\n    { name: 'Privacy Policy', href: '/privacy' },\n    { name: 'Contact Us', href: '/contact' },\n    { name: 'Help', href: '/help' },\n    { name: 'DMCA', href: '/dmca' },\n  ];\n\n  const socialLinks = [\n    {\n      name: 'Twitter',\n      href: '#',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'Instagram',\n      href: '#',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.807-.875-1.297-2.026-1.297-3.323s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323z\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'Discord',\n      href: '#',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0189 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z\" />\n        </svg>\n      ),\n    },\n  ];\n\n  return (\n    <footer className={cn(\n      \"border-t border-border py-8 px-6 md:px-10 bg-background-secondary\",\n      className\n    )}>\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8\">\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-text font-semibold mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/categories\" className=\"text-text-secondary hover:text-primary text-sm transition-colors\">\n                  Browse Categories\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/models\" className=\"text-text-secondary hover:text-primary text-sm transition-colors\">\n                  Top Models\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/premium\" className=\"text-text-secondary hover:text-primary text-sm transition-colors\">\n                  Premium Content\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/upload\" className=\"text-text-secondary hover:text-primary text-sm transition-colors\">\n                  Upload Content\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-text font-semibold mb-4\">Support</h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/help\" className=\"text-text-secondary hover:text-primary text-sm transition-colors\">\n                  Help Center\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-text-secondary hover:text-primary text-sm transition-colors\">\n                  Contact Support\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/report\" className=\"text-text-secondary hover:text-primary text-sm transition-colors\">\n                  Report Content\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/feedback\" className=\"text-text-secondary hover:text-primary text-sm transition-colors\">\n                  Send Feedback\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Age Verification Notice */}\n        <div className=\"bg-background-input rounded-lg p-4 mb-6\">\n          <div className=\"flex items-start gap-3\">\n            <div className=\"text-warning flex-shrink-0 mt-0.5\">\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n              </svg>\n            </div>\n            <div>\n              <h4 className=\"text-text font-semibold text-sm mb-1\">Age Verification Required</h4>\n              <p className=\"text-text-secondary text-xs leading-relaxed\">\n                This website contains adult content. By accessing this site, you confirm that you are 18 years or older\n                and agree to our terms of service. All models are 18+ years old.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"flex flex-col md:flex-row justify-between items-center pt-6 border-t border-border\">\n          {/* Copyright */}\n          <p className=\"text-text-secondary text-xs mb-4 md:mb-0\">\n            © {currentYear} All rights reserved.\n          </p>\n\n          {/* Legal Links */}\n          <div className=\"flex flex-wrap items-center gap-4 mb-4 md:mb-0\">\n            {footerLinks.map((link, index) => (\n              <React.Fragment key={link.name}>\n                <Link\n                  href={link.href}\n                  className=\"text-text-muted hover:text-text text-xs transition-colors\"\n                >\n                  {link.name}\n                </Link>\n                {index < footerLinks.length - 1 && (\n                  <span className=\"text-text-muted text-xs\">•</span>\n                )}\n              </React.Fragment>\n            ))}\n          </div>\n\n          {/* Social Links */}\n          <div className=\"flex items-center gap-3\">\n            {socialLinks.map((social) => (\n              <Link\n                key={social.name}\n                href={social.href}\n                className=\"text-text-secondary hover:text-primary transition-colors\"\n                aria-label={social.name}\n              >\n                {social.icon}\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAMO,SAAS,OAAO,EAAE,SAAS,EAAe;IAC/C,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB;YAAE,MAAM;YAAoB,MAAM;QAAS;QAC3C;YAAE,MAAM;YAAkB,MAAM;QAAW;QAC3C;YAAE,MAAM;YAAc,MAAM;QAAW;QACvC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAQ;KAC/B;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;gBAAY,OAAM;0BACrE,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;gBAAY,OAAM;0BACrE,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;gBAAY,OAAM;0BACrE,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;KACD;IAED,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,qEACA;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAmE;;;;;;;;;;;sDAIxG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmE;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmE;;;;;;;;;;;sDAIrG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmE;;;;;;;;;;;;;;;;;;;;;;;sCAQxG,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmE;;;;;;;;;;;sDAIlG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmE;;;;;;;;;;;sDAIrG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmE;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAmE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS5G,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAe,SAAQ;8CACnD,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAGZ,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,6LAAC;wCAAE,WAAU;kDAA8C;;;;;;;;;;;;;;;;;;;;;;;8BASjE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAE,WAAU;;gCAA2C;gCACnD;gCAAY;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,IAAI;;;;;;wCAEX,QAAQ,YAAY,MAAM,GAAG,mBAC5B,6LAAC;4CAAK,WAAU;sDAA0B;;;;;;;mCARzB,KAAK,IAAI;;;;;;;;;;sCAelC,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,OAAO,IAAI;oCACjB,WAAU;oCACV,cAAY,OAAO,IAAI;8CAEtB,OAAO,IAAI;mCALP,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAahC;KApKgB", "debugId": null}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/layout/MainLayout.tsx"], "sourcesContent": ["import React from 'react';\nimport { Head<PERSON> } from './Header';\nimport { Footer } from './Footer';\nimport { cn } from '@/lib/utils';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n  className?: string;\n  showHeader?: boolean;\n  showFooter?: boolean;\n}\n\nexport function MainLayout({ \n  children, \n  className,\n  showHeader = true,\n  showFooter = true \n}: MainLayoutProps) {\n  return (\n    <div className={cn(\n      \"relative flex size-full min-h-screen flex-col bg-background group/design-root overflow-x-hidden\",\n      className\n    )}>\n      <div className=\"layout-container flex h-full grow flex-col\">\n        {showHeader && <Header />}\n        \n        <main className=\"flex flex-1 flex-col\">\n          {children}\n        </main>\n        \n        {showFooter && <Footer />}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AASO,SAAS,WAAW,EACzB,QAAQ,EACR,SAAS,EACT,aAAa,IAAI,EACjB,aAAa,IAAI,EACD;IAChB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,mGACA;kBAEA,cAAA,6LAAC;YAAI,WAAU;;gBACZ,4BAAc,6LAAC,yIAAA,CAAA,SAAM;;;;;8BAEtB,6LAAC;oBAAK,WAAU;8BACb;;;;;;gBAGF,4BAAc,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;;;;;;AAI9B;KAtBgB", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/ui/VideoCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { cn, formatDuration, formatViews, formatTimeAgo } from '@/lib/utils';\n\ninterface VideoCardProps {\n  id: string;\n  title: string;\n  thumbnail: string;\n  duration: number; // in seconds\n  views?: number;\n  uploadedAt?: Date;\n  category?: string;\n  isHD?: boolean;\n  isPremium?: boolean;\n  className?: string;\n  onClick?: () => void;\n}\n\nexport function VideoCard({\n  id,\n  title,\n  thumbnail,\n  duration,\n  views,\n  uploadedAt,\n  category,\n  isHD = false,\n  isPremium = false,\n  className,\n  onClick,\n}: VideoCardProps) {\n  const handleClick = () => {\n    if (onClick) {\n      onClick();\n    }\n  };\n\n  const cardContent = (\n    <div className={cn(\"flex flex-col gap-2 group cursor-pointer\", className)}>\n      {/* Thumbnail Container */}\n      <div className=\"relative w-full aspect-video rounded-lg overflow-hidden shadow-lg group-hover:shadow-primary/30 transition-shadow\">\n        {/* Thumbnail Image */}\n        <div className=\"relative w-full h-full\">\n          <Image\n            src={thumbnail}\n            alt={title}\n            fill\n            className=\"object-cover transition-transform duration-300 group-hover:scale-105\"\n            sizes=\"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw\"\n          />\n          \n          {/* Overlay Gradient */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n        </div>\n\n        {/* Duration Badge */}\n        <div className=\"video-thumbnail-overlay\">\n          {formatDuration(duration)}\n        </div>\n\n        {/* Quality Badges */}\n        <div className=\"absolute top-2 left-2 flex gap-1\">\n          {isHD && (\n            <span className=\"bg-primary text-white text-xs font-bold px-1.5 py-0.5 rounded\">\n              HD\n            </span>\n          )}\n          {isPremium && (\n            <span className=\"bg-warning text-black text-xs font-bold px-1.5 py-0.5 rounded\">\n              PREMIUM\n            </span>\n          )}\n        </div>\n\n        {/* Play Button Overlay */}\n        <div className=\"video-thumbnail-overlay-play\">\n          <svg\n            fill=\"white\"\n            height=\"32\"\n            viewBox=\"0 0 256 256\"\n            width=\"32\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path d=\"M240,128a15.74,15.74,0,0,1-7.6,13.51L88.32,229.65a16,16,0,0,1-24.32-13.51V40A16,16,0,0,1,88.32,26.35l144.08,88.14A15.74,15.74,0,0,1,240,128Z\" />\n          </svg>\n        </div>\n\n        {/* Hover Actions */}\n        <div className=\"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n          <div className=\"flex gap-1\">\n            {/* Favorite Button */}\n            <button\n              className=\"bg-black/60 hover:bg-black/80 text-white p-1.5 rounded-full transition-colors\"\n              onClick={(e) => {\n                e.preventDefault();\n                e.stopPropagation();\n                // Handle favorite action\n              }}\n              aria-label=\"Add to favorites\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"/>\n              </svg>\n            </button>\n\n            {/* Watch Later Button */}\n            <button\n              className=\"bg-black/60 hover:bg-black/80 text-white p-1.5 rounded-full transition-colors\"\n              onClick={(e) => {\n                e.preventDefault();\n                e.stopPropagation();\n                // Handle watch later action\n              }}\n              aria-label=\"Watch later\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Video Info */}\n      <div className=\"space-y-1\">\n        {/* Title */}\n        <h3 className=\"text-text text-sm font-semibold leading-tight line-clamp-2 group-hover:text-primary transition-colors\">\n          {title}\n        </h3>\n\n        {/* Metadata */}\n        <div className=\"flex items-center justify-between text-xs\">\n          <div className=\"flex items-center gap-2 text-text-muted\">\n            {views && (\n              <span>{formatViews(views)} views</span>\n            )}\n            {views && uploadedAt && (\n              <span>•</span>\n            )}\n            {uploadedAt && (\n              <span>{formatTimeAgo(uploadedAt)}</span>\n            )}\n          </div>\n          \n          {category && (\n            <span className=\"text-text-secondary text-xs bg-background-input px-2 py-0.5 rounded\">\n              {category}\n            </span>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  if (onClick) {\n    return (\n      <div onClick={handleClick}>\n        {cardContent}\n      </div>\n    );\n  }\n\n  return (\n    <Link href={`/video/${id}`} className=\"block\">\n      {cardContent}\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAgBO,SAAS,UAAU,EACxB,EAAE,EACF,KAAK,EACL,SAAS,EACT,QAAQ,EACR,KAAK,EACL,UAAU,EACV,QAAQ,EACR,OAAO,KAAK,EACZ,YAAY,KAAK,EACjB,SAAS,EACT,OAAO,EACQ;IACf,MAAM,cAAc;QAClB,IAAI,SAAS;YACX;QACF;IACF;IAEA,MAAM,4BACJ,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;;0BAE7D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAK;gCACL,IAAI;gCACJ,WAAU;gCACV,OAAM;;;;;;0CAIR,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;kCAIlB,6LAAC;wBAAI,WAAU;;4BACZ,sBACC,6LAAC;gCAAK,WAAU;0CAAgE;;;;;;4BAIjF,2BACC,6LAAC;gCAAK,WAAU;0CAAgE;;;;;;;;;;;;kCAOpF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,OAAM;4BACN,OAAM;sCAEN,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,EAAE,eAAe;oCACjB,yBAAyB;oCAC3B;oCACA,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAKZ,6LAAC;oCACC,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,EAAE,eAAe;oCACjB,4BAA4B;oCAC9B;oCACA,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAG,WAAU;kCACX;;;;;;kCAIH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,uBACC,6LAAC;;4CAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;4CAAO;;;;;;;oCAE3B,SAAS,4BACR,6LAAC;kDAAK;;;;;;oCAEP,4BACC,6LAAC;kDAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;;;;;;;;;;;;4BAIxB,0BACC,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;;;;;;;IAQb,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,SAAS;sBACX;;;;;;IAGP;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,OAAO,EAAE,IAAI;QAAE,WAAU;kBACnC;;;;;;AAGP;KArJgB", "debugId": null}}, {"offset": {"line": 1430, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { MainLayout } from '@/components/layout/MainLayout';\nimport { VideoCard } from '@/components/ui/VideoCard';\nimport { Button } from '@/components/ui/Button';\n\n// Mock data for demonstration\nconst mockVideos = [\n  {\n    id: '1',\n    title: 'Passionate Encounter',\n    thumbnail: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=225&fit=crop',\n    duration: 754, // 12:34\n    views: 125000,\n    uploadedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago\n    category: 'Romance',\n    isHD: true,\n  },\n  {\n    id: '2',\n    title: 'Late Night Rendezvous',\n    thumbnail: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?w=400&h=225&fit=crop',\n    duration: 922, // 15:22\n    views: 89000,\n    uploadedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago\n    category: 'Drama',\n    isPremium: true,\n  },\n  {\n    id: '3',\n    title: 'Secret Affair',\n    thumbnail: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=225&fit=crop',\n    duration: 611, // 10:11\n    views: 234000,\n    uploadedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago\n    category: 'Thriller',\n    isHD: true,\n  },\n  {\n    id: '4',\n    title: 'Weekend Getaway',\n    thumbnail: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=225&fit=crop',\n    duration: 896, // 14:56\n    views: 156000,\n    uploadedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago\n    category: 'Adventure',\n  },\n  {\n    id: '5',\n    title: 'Hidden Desires',\n    thumbnail: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=225&fit=crop',\n    duration: 693, // 11:33\n    views: 78000,\n    uploadedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 2 weeks ago\n    category: 'Mystery',\n    isPremium: true,\n  },\n  {\n    id: '6',\n    title: 'Forbidden Love',\n    thumbnail: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=225&fit=crop',\n    duration: 825, // 13:45\n    views: 345000,\n    uploadedAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000), // 3 weeks ago\n    category: 'Romance',\n    isHD: true,\n  },\n];\n\nconst categories = ['Trending', 'Newest', 'Most Viewed', 'Top Rated'];\n\nexport default function Home() {\n  const [activeCategory, setActiveCategory] = useState('Trending');\n\n  return (\n    <MainLayout>\n      {/* Main Content */}\n      <div className=\"px-4 sm:px-6 md:px-10 lg:px-16 xl:px-24 2xl:px-40 flex flex-1 justify-center py-5\">\n        <div className=\"layout-content-container flex flex-col w-full max-w-screen-xl\">\n          {/* Category Tabs */}\n          <div className=\"pb-3\">\n            <div className=\"flex border-b border-border px-2 sm:px-4 gap-4 sm:gap-8\">\n              {categories.map((category) => (\n                <button\n                  key={category}\n                  onClick={() => setActiveCategory(category)}\n                  className={`flex flex-col items-center justify-center border-b-[3px] pb-3 pt-4 group transition-colors ${\n                    activeCategory === category\n                      ? 'border-b-primary text-text'\n                      : 'border-b-transparent text-text-secondary hover:text-text hover:border-b-primary/50'\n                  }`}\n                >\n                  <p className=\"text-sm font-bold leading-normal tracking-[0.015em]\">\n                    {category}\n                  </p>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Video Grid */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4 p-2 sm:p-4\">\n            {mockVideos.map((video) => (\n              <VideoCard\n                key={video.id}\n                id={video.id}\n                title={video.title}\n                thumbnail={video.thumbnail}\n                duration={video.duration}\n                views={video.views}\n                uploadedAt={video.uploadedAt}\n                category={video.category}\n                isHD={video.isHD}\n                isPremium={video.isPremium}\n              />\n            ))}\n          </div>\n\n          {/* Load More Button */}\n          <div className=\"flex justify-center mt-8\">\n            <Button variant=\"outline\" size=\"lg\">\n              Load More Videos\n            </Button>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,8BAA8B;AAC9B,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;QACrD,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;QACrD,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;QACrD,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;QACtD,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;QACtD,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;QACtD,UAAU;QACV,MAAM;IACR;CACD;AAED,MAAM,aAAa;IAAC;IAAY;IAAU;IAAe;CAAY;AAEtD,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,6LAAC,6IAAA,CAAA,aAAU;kBAET,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;oCAEC,SAAS,IAAM,kBAAkB;oCACjC,WAAW,CAAC,2FAA2F,EACrG,mBAAmB,WACf,+BACA,sFACJ;8CAEF,cAAA,6LAAC;wCAAE,WAAU;kDACV;;;;;;mCATE;;;;;;;;;;;;;;;kCAiBb,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,sBACf,6LAAC,wIAAA,CAAA,YAAS;gCAER,IAAI,MAAM,EAAE;gCACZ,OAAO,MAAM,KAAK;gCAClB,WAAW,MAAM,SAAS;gCAC1B,UAAU,MAAM,QAAQ;gCACxB,OAAO,MAAM,KAAK;gCAClB,YAAY,MAAM,UAAU;gCAC5B,UAAU,MAAM,QAAQ;gCACxB,MAAM,MAAM,IAAI;gCAChB,WAAW,MAAM,SAAS;+BATrB,MAAM,EAAE;;;;;;;;;;kCAenB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;sCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GAzDwB;KAAA", "debugId": null}}]}