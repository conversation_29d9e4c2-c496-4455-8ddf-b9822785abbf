import React from 'react';
import { Head<PERSON> } from './Header';
import { Footer } from './Footer';
import { cn } from '@/lib/utils';

interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
  showHeader?: boolean;
  showFooter?: boolean;
}

export function MainLayout({ 
  children, 
  className,
  showHeader = true,
  showFooter = true 
}: MainLayoutProps) {
  return (
    <div className={cn(
      "relative flex size-full min-h-screen flex-col bg-background group/design-root overflow-x-hidden",
      className
    )}>
      <div className="layout-container flex h-full grow flex-col">
        {showHeader && <Header />}
        
        <main className="flex flex-1 flex-col">
          {children}
        </main>
        
        {showFooter && <Footer />}
      </div>
    </div>
  );
}
