import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { cn, formatDuration, formatViews, formatTimeAgo } from '@/lib/utils';

interface VideoCardProps {
  id: string;
  title: string;
  thumbnail: string;
  duration: number; // in seconds
  views?: number;
  uploadedAt?: Date;
  category?: string;
  isHD?: boolean;
  isPremium?: boolean;
  className?: string;
  onClick?: () => void;
}

export function VideoCard({
  id,
  title,
  thumbnail,
  duration,
  views,
  uploadedAt,
  category,
  isHD = false,
  isPremium = false,
  className,
  onClick,
}: VideoCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  const cardContent = (
    <div className={cn("flex flex-col gap-2 group cursor-pointer", className)}>
      {/* Thumbnail Container */}
      <div className="relative w-full aspect-video rounded-lg overflow-hidden shadow-lg group-hover:shadow-primary/30 transition-shadow">
        {/* Thumbnail Image */}
        <div className="relative w-full h-full">
          <Image
            src={thumbnail}
            alt={title}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
          />
          
          {/* Overlay Gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        {/* Duration Badge */}
        <div className="video-thumbnail-overlay">
          {formatDuration(duration)}
        </div>

        {/* Quality Badges */}
        <div className="absolute top-2 left-2 flex gap-1">
          {isHD && (
            <span className="bg-primary text-white text-xs font-bold px-1.5 py-0.5 rounded">
              HD
            </span>
          )}
          {isPremium && (
            <span className="bg-warning text-black text-xs font-bold px-1.5 py-0.5 rounded">
              PREMIUM
            </span>
          )}
        </div>

        {/* Play Button Overlay */}
        <div className="video-thumbnail-overlay-play">
          <svg
            fill="white"
            height="32"
            viewBox="0 0 256 256"
            width="32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M240,128a15.74,15.74,0,0,1-7.6,13.51L88.32,229.65a16,16,0,0,1-24.32-13.51V40A16,16,0,0,1,88.32,26.35l144.08,88.14A15.74,15.74,0,0,1,240,128Z" />
          </svg>
        </div>

        {/* Hover Actions */}
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="flex gap-1">
            {/* Favorite Button */}
            <button
              className="bg-black/60 hover:bg-black/80 text-white p-1.5 rounded-full transition-colors"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Handle favorite action
              }}
              aria-label="Add to favorites"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
              </svg>
            </button>

            {/* Watch Later Button */}
            <button
              className="bg-black/60 hover:bg-black/80 text-white p-1.5 rounded-full transition-colors"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Handle watch later action
              }}
              aria-label="Watch later"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Video Info */}
      <div className="space-y-1">
        {/* Title */}
        <h3 className="text-text text-sm font-semibold leading-tight line-clamp-2 group-hover:text-primary transition-colors">
          {title}
        </h3>

        {/* Metadata */}
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-2 text-text-muted">
            {views && (
              <span>{formatViews(views)} views</span>
            )}
            {views && uploadedAt && (
              <span>•</span>
            )}
            {uploadedAt && (
              <span>{formatTimeAgo(uploadedAt)}</span>
            )}
          </div>
          
          {category && (
            <span className="text-text-secondary text-xs bg-background-input px-2 py-0.5 rounded">
              {category}
            </span>
          )}
        </div>
      </div>
    </div>
  );

  if (onClick) {
    return (
      <div onClick={handleClick}>
        {cardContent}
      </div>
    );
  }

  return (
    <Link href={`/video/${id}`} className="block">
      {cardContent}
    </Link>
  );
}
