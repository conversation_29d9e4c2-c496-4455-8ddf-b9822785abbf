'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { MainLayout } from '@/components/layout/MainLayout';
import { cn } from '@/lib/utils';

interface Category {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  videoCount: number;
  isPopular?: boolean;
}

const categories: Category[] = [
  {
    id: 'amateur',
    name: 'Amateur',
    description: 'Real people, real passion',
    thumbnail: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop',
    videoCount: 15420,
    isPopular: true,
  },
  {
    id: 'milf',
    name: 'MILF',
    description: 'Experienced and confident',
    thumbnail: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?w=300&h=200&fit=crop',
    videoCount: 8934,
    isPopular: true,
  },
  {
    id: 'teen',
    name: 'Teen (18+)',
    description: 'Young and energetic',
    thumbnail: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop',
    videoCount: 12567,
    isPopular: true,
  },
  {
    id: 'asian',
    name: 'Asian',
    description: 'Beautiful Asian performers',
    thumbnail: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=300&h=200&fit=crop',
    videoCount: 6789,
  },
  {
    id: 'latina',
    name: 'Latina',
    description: 'Passionate Latin performers',
    thumbnail: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=200&fit=crop',
    videoCount: 5432,
  },
  {
    id: 'blonde',
    name: 'Blonde',
    description: 'Golden hair, golden moments',
    thumbnail: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=300&h=200&fit=crop',
    videoCount: 9876,
  },
  {
    id: 'brunette',
    name: 'Brunette',
    description: 'Dark hair, deep passion',
    thumbnail: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop',
    videoCount: 8765,
  },
  {
    id: 'big-tits',
    name: 'Big Tits',
    description: 'Voluptuous and beautiful',
    thumbnail: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?w=300&h=200&fit=crop',
    videoCount: 11234,
  },
  {
    id: 'anal',
    name: 'Anal',
    description: 'Intense backdoor action',
    thumbnail: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop',
    videoCount: 7890,
  },
  {
    id: 'threesome',
    name: 'Threesome',
    description: 'More is always better',
    thumbnail: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=300&h=200&fit=crop',
    videoCount: 4567,
  },
  {
    id: 'lesbian',
    name: 'Lesbian',
    description: 'Women loving women',
    thumbnail: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=200&fit=crop',
    videoCount: 6543,
  },
  {
    id: 'hardcore',
    name: 'Hardcore',
    description: 'Intense and passionate',
    thumbnail: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=300&h=200&fit=crop',
    videoCount: 13456,
  },
];

function CategoryCard({ category }: { category: Category }) {
  return (
    <Link href={`/categories/${category.id}`} className="category-card block">
      <div className="relative group cursor-pointer">
        {/* Thumbnail */}
        <div className="relative w-full aspect-[3/2] rounded-lg overflow-hidden shadow-lg group-hover:shadow-primary/30 transition-shadow">
          <Image
            src={category.thumbnail}
            alt={category.name}
            fill
            className="category-image object-cover transition-all duration-300"
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
          />
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
          
          {/* Popular Badge */}
          {category.isPopular && (
            <div className="absolute top-3 left-3">
              <span className="bg-primary text-white text-xs font-bold px-2 py-1 rounded-full">
                POPULAR
              </span>
            </div>
          )}
          
          {/* Content */}
          <div className="absolute bottom-0 left-0 right-0 p-4">
            <h3 className="category-title text-white text-lg font-bold mb-1 transition-colors">
              {category.name}
            </h3>
            <p className="text-gray-300 text-sm mb-2 line-clamp-2">
              {category.description}
            </p>
            <p className="text-gray-400 text-xs">
              {category.videoCount.toLocaleString()} videos
            </p>
          </div>
        </div>
      </div>
    </Link>
  );
}

export default function CategoriesPage() {
  const popularCategories = categories.filter(cat => cat.isPopular);
  const allCategories = categories;

  return (
    <MainLayout>
      <div className="px-4 sm:px-6 md:px-10 lg:px-16 xl:px-24 2xl:px-40 flex flex-1 justify-center py-8">
        <div className="layout-content-container flex flex-col w-full max-w-screen-xl">
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-text text-3xl md:text-4xl font-bold mb-4">
              Browse Categories
            </h1>
            <p className="text-text-secondary text-lg max-w-2xl">
              Discover content organized by your favorite categories. From amateur to professional, 
              find exactly what you're looking for.
            </p>
          </div>

          {/* Popular Categories */}
          <section className="mb-12">
            <h2 className="text-text text-2xl font-bold mb-6 flex items-center gap-2">
              <span className="text-primary">🔥</span>
              Popular Categories
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {popularCategories.map((category) => (
                <CategoryCard key={category.id} category={category} />
              ))}
            </div>
          </section>

          {/* All Categories */}
          <section>
            <h2 className="text-text text-2xl font-bold mb-6">
              All Categories
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {allCategories.map((category) => (
                <CategoryCard key={category.id} category={category} />
              ))}
            </div>
          </section>
        </div>
      </div>
    </MainLayout>
  );
}
