'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import { MainLayout } from '@/components/layout/MainLayout';
import { VideoCard } from '@/components/ui/VideoCard';
import { Button } from '@/components/ui/Button';
import { formatViews, formatTimeAgo } from '@/lib/utils';

// Mock video data
const mockVideo = {
  id: '1',
  title: 'Passionate Encounter - Full HD Experience',
  description: 'Experience the ultimate in romantic passion with this stunning HD video featuring beautiful performers in an intimate setting. This premium content showcases the art of love-making with exceptional cinematography and authentic chemistry.',
  duration: 754, // 12:34
  views: 125000,
  likes: 8934,
  dislikes: 234,
  uploadedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
  category: 'Romance',
  tags: ['passionate', 'romantic', 'hd', 'intimate', 'couple'],
  isHD: true,
  isPremium: false,
  videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
  thumbnail: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=450&fit=crop',
  uploader: {
    name: 'PremiumStudios',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
    verified: true,
    subscribers: 45600,
  }
};

// Mock related videos
const relatedVideos = [
  {
    id: '2',
    title: 'Late Night Rendezvous',
    thumbnail: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?w=400&h=225&fit=crop',
    duration: 922,
    views: 89000,
    uploadedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    category: 'Drama',
    isPremium: true,
  },
  {
    id: '3',
    title: 'Secret Affair',
    thumbnail: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=225&fit=crop',
    duration: 611,
    views: 234000,
    uploadedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    category: 'Thriller',
    isHD: true,
  },
  {
    id: '4',
    title: 'Weekend Getaway',
    thumbnail: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=225&fit=crop',
    duration: 896,
    views: 156000,
    uploadedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
    category: 'Adventure',
  },
];

export default function VideoPlayerPage() {
  const params = useParams();
  const [isLiked, setIsLiked] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [showDescription, setShowDescription] = useState(false);

  const handleLike = () => {
    setIsLiked(!isLiked);
    if (isDisliked) setIsDisliked(false);
  };

  const handleDislike = () => {
    setIsDisliked(!isDisliked);
    if (isLiked) setIsLiked(false);
  };

  return (
    <MainLayout>
      <div className="px-4 sm:px-6 md:px-10 lg:px-16 xl:px-24 2xl:px-40 flex flex-1 justify-center py-6">
        <div className="layout-content-container flex flex-col lg:flex-row w-full max-w-screen-2xl gap-8">
          {/* Main Video Section */}
          <div className="flex-1">
            {/* Video Player */}
            <div className="relative w-full aspect-video bg-black rounded-lg overflow-hidden shadow-2xl mb-6">
              <video
                className="w-full h-full object-cover"
                controls
                poster={mockVideo.thumbnail}
                preload="metadata"
              >
                <source src={mockVideo.videoUrl} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </div>

            {/* Video Info */}
            <div className="space-y-4">
              {/* Title and Badges */}
              <div className="flex flex-wrap items-start gap-3">
                <h1 className="text-text text-xl md:text-2xl font-bold flex-1 min-w-0">
                  {mockVideo.title}
                </h1>
                <div className="flex gap-2">
                  {mockVideo.isHD && (
                    <span className="bg-primary text-white text-xs font-bold px-2 py-1 rounded">
                      HD
                    </span>
                  )}
                  {mockVideo.isPremium && (
                    <span className="bg-warning text-black text-xs font-bold px-2 py-1 rounded">
                      PREMIUM
                    </span>
                  )}
                </div>
              </div>

              {/* Stats and Actions */}
              <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="flex items-center gap-4 text-text-secondary text-sm">
                  <span>{formatViews(mockVideo.views)} views</span>
                  <span>•</span>
                  <span>{formatTimeAgo(mockVideo.uploadedAt)}</span>
                </div>

                <div className="flex items-center gap-2">
                  {/* Like/Dislike */}
                  <div className="flex items-center bg-background-input rounded-lg overflow-hidden">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleLike}
                      className={`rounded-none px-4 ${isLiked ? 'text-primary' : 'text-text-secondary'}`}
                    >
                      <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7.493 18.75c-.425 0-.82-.236-.975-.632A7.48 7.48 0 016 15.375c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75 2.25 2.25 0 012.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558-.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23h-.777zM2.331 10.977a11.969 11.969 0 00-.831 4.398 12 12 0 00.52 3.507c.26.85 1.084 1.368 1.973 1.368H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 01-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227z"/>
                      </svg>
                      {mockVideo.likes + (isLiked ? 1 : 0)}
                    </Button>
                    <div className="w-px h-6 bg-border" />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleDislike}
                      className={`rounded-none px-4 ${isDisliked ? 'text-primary' : 'text-text-secondary'}`}
                    >
                      <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M15.73 5.25h1.035A7.465 7.465 0 0118 9.375a7.465 7.465 0 01-1.235 4.125h-.148c-.806 0-1.534.446-2.031 1.08a9.04 9.04 0 01-2.861 2.4c-.723.384-1.35.956-1.653 1.715a4.498 4.498 0 00-.322 1.672V21a.75.75 0 01-.75.75 2.25 2.25 0 01-2.25-2.25c0-1.152.26-2.243.723-3.218C7.74 15.724 7.366 15 6.748 15H3.622c-1.026 0-1.945-.694-2.054-1.715A12.134 12.134 0 011.5 12c0-2.848.992-5.464 2.649-7.521C4.537 3.997 5.136 3.75 5.754 3.75h9.776c.483 0 .964.078 1.423.23l3.114 1.04a4.501 4.501 0 001.423.23h.777zM21.669 13.023c.536-1.362.831-2.845.831-4.398 0-1.22-.182-2.398-.52-3.507-.26-.85-1.084-1.368-1.973-1.368H19.1c-.445 0-.72.498-.523.898.591 1.2.924 2.55.924 3.977a8.958 8.958 0 01-1.302 4.666c-.245.403.028.959.5.959h1.053c.832 0 1.612-.453 1.918-1.227z"/>
                      </svg>
                      {mockVideo.dislikes + (isDisliked ? 1 : 0)}
                    </Button>
                  </div>

                  {/* Favorite */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsFavorited(!isFavorited)}
                    className={`${isFavorited ? 'text-primary' : 'text-text-secondary'}`}
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                    </svg>
                  </Button>

                  {/* Share */}
                  <Button variant="ghost" size="sm" className="text-text-secondary">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"/>
                    </svg>
                  </Button>
                </div>
              </div>

              {/* Uploader Info */}
              <div className="flex items-center justify-between p-4 bg-background-card rounded-lg">
                <div className="flex items-center gap-3">
                  <div
                    className="w-12 h-12 rounded-full bg-cover bg-center border-2 border-primary"
                    style={{ backgroundImage: `url("${mockVideo.uploader.avatar}")` }}
                  />
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="text-text font-semibold">{mockVideo.uploader.name}</h3>
                      {mockVideo.uploader.verified && (
                        <svg className="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                      )}
                    </div>
                    <p className="text-text-secondary text-sm">
                      {mockVideo.uploader.subscribers.toLocaleString()} subscribers
                    </p>
                  </div>
                </div>
                <Button variant="primary" size="sm">
                  Subscribe
                </Button>
              </div>

              {/* Description */}
              <div className="bg-background-card rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-text font-semibold">Description</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowDescription(!showDescription)}
                  >
                    {showDescription ? 'Show less' : 'Show more'}
                  </Button>
                </div>
                <p className={`text-text-secondary text-sm leading-relaxed ${
                  showDescription ? '' : 'line-clamp-3'
                }`}>
                  {mockVideo.description}
                </p>
                
                {/* Tags */}
                <div className="flex flex-wrap gap-2 mt-4">
                  {mockVideo.tags.map((tag) => (
                    <span
                      key={tag}
                      className="bg-background-input text-text-secondary text-xs px-2 py-1 rounded hover:bg-background-hover transition-colors cursor-pointer"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar - Related Videos */}
          <div className="lg:w-80 xl:w-96">
            <h2 className="text-text text-lg font-bold mb-4">Related Videos</h2>
            <div className="space-y-4">
              {relatedVideos.map((video) => (
                <div key={video.id} className="flex gap-3">
                  <div className="w-32 flex-shrink-0">
                    <VideoCard
                      id={video.id}
                      title={video.title}
                      thumbnail={video.thumbnail}
                      duration={video.duration}
                      views={video.views}
                      uploadedAt={video.uploadedAt}
                      category={video.category}
                      isHD={video.isHD}
                      isPremium={video.isPremium}
                      className="scale-75 origin-top-left"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
